import { Component, OnInit, Input } from "@angular/core";
import { FormControl } from "@angular/forms";
import { LocalizationService } from "@base-core/localization/localization.service";
import createNumberMask from "text-mask-addons/dist/createNumberMask";

@Component({
	selector: "pacto-avaliacao-input-number",
	templateUrl: "./avaliacao-input-number.component.html",
	styleUrls: ["./avaliacao-input-number.component.scss"],
})
export class AvaliacaoInputNumberComponent implements OnInit {
	@Input() decimal = true;
	@Input() control: FormControl;
	@Input() largeInput = false;
	@Input() id: string;
	textMask = { mask: false, guide: false };

	constructor(private localization: LocalizationService) {}

	ngOnInit() {
		if (!this.control) {
			throw new Error("Missing input: control.");
		}
		this.control.markAsPristine();
		this.configureMask();
		this.control.valueChanges.subscribe((value: string) => {
			this.process(value);
		});
		setTimeout(() => {
			if (this.control.value) {
				this.process(this.control.value);
			}
		});
	}

	selectLine($event) {
		$event.target.select();
	}

	private process(value) {
		if (this.control.pristine) {
			this.processInitialDataSetup(value);
			if (typeof value === "string") {
				const finalValue = this.normalizeToFloat(value);
				this.control.setValue(finalValue, {
					emitEvent: false,
					emitModelToViewChange: false,
				});
			} else {
				const finalValue = value != null ? Number(value) : null;
				this.control.setValue(finalValue, {
					emitEvent: false,
					emitModelToViewChange: false,
				});
			}
		} else {
			this.processView(value);
		}
	}

	private processInitialDataSetup(value) {
		const brazil = this.localization.getLocaleDecimalSymbol() === ",";
		if (brazil) {
			const valueString = `${value}`;
			const newValue = valueString.replace(".", ",");
			this.control.setValue(newValue, {
				emitEvent: false,
				emitViewToModelChange: false,
			});
		}
	}

	private processView(value: unknown) {
		const finalValue = this.normalizeToFloat(value as any);
		this.control.setValue(finalValue, {
			emitEvent: false,
			emitModelToViewChange: false,
		});
	}

	private configureMask() {
		const thousands = this.localization.getLocalethousandsSeparatorSymbol();
		const decimals = this.localization.getLocaleDecimalSymbol();
		this.textMask.mask = createNumberMask({
			prefix: "",
			includeThousandsSeparator: true,
			integerLimit: 6,
			allowDecimal: this.decimal,
			thousandsSeparatorSymbol: thousands,
			decimalSymbol: decimals,
		});
	}

	private normalizeToFloat(
		raw: string | number | null | undefined
	): number | null {
		if (raw == null) return null;
		if (typeof raw === "number") {
			return Number.isFinite(raw) ? raw : null;
		}
		let s = String(raw).trim();
		const thousands = this.localization.getLocalethousandsSeparatorSymbol();
		const decimal = this.localization.getLocaleDecimalSymbol();

		// remove todos os separadores de milhar (split/join evita escapar regex e cobre todos os casos)
		if (thousands) {
			s = s.split(thousands).join("");
		}

		// converte o separador decimal local para ponto
		if (decimal && decimal !== ".") {
			s = s.replace(decimal, ".");
		} else {
			// fallback: se sobrar vírgula simples, converte (ex.: dados inconsistentes)
			s = s.replace(/,/, ".");
		}

		if (!s) return null;
		const n = Number.parseFloat(s);
		return s ? parseFloat(s) : null;
		return Number.isFinite(n) ? n : null;
	}
}
