import { Component, OnInit } from "@angular/core";
import { NgbDatepickerConfig } from "@ng-bootstrap/ng-bootstrap";
import { ScriptLoaderService } from "sdk";

@Component({
	selector: "pacto-root",
	templateUrl: "./app.component.html",
})
export class AppComponent implements OnInit {
	title = "app";

	constructor(
		private ngbDatepickerConfig: NgbDatepickerConfig,
		private scriptLoaderService: ScriptLoaderService
	) {
		this.ngbDatepickerConfig.minDate = { year: 1958, month: 1, day: 1 };
	}

	async ngOnInit() {
		this.setupMobileLayout();
		await this.scriptLoaderService.loadScripts();
	}

	private setupMobileLayout() {
		console.log("setupMobileLayout");
		const isMobile =
			window.innerWidth < 1280 &&
			/Android|iPhone|iPad|iPod/i.test(navigator.userAgent);
		if (isMobile) {
			const meta = document.querySelector("meta[name=viewport]");
			if (meta) {
				meta.setAttribute("content", "width=1280, initial-scale=0.1");
			}
			const body = document.querySelector("body");
			if (body) {
				body.classList.add("body-scale-7");
			}
		}
	}
}
