<adm-layout
	(goBack)="voltarHome()"
	i18n-modulo="@@servidor-facial:modulo"
	i18n-pageTitle="@@servidor-facial:title"
	modulo="Administrativo"
	pageTitle="Servidor facial">
	<div class="table-wrapper">
		<pacto-relatorio
			#tableDataComponent
			(btnAddClick)="novo()"
			(iconClick)="iconClickFn($event)"
			(rowClick)="edit($event)"
			[actionTitulo]="traducao.getLabel('table-column-acoes')"
			[enableDs3]="true"
			[filterConfig]="filterConfig"
			[labelBtnAdd]="'Adicionar'"
			[showBtnAdd]="true"
			[showShare]="true"
			[table]="tableData"
			telaId="servidorFacial"></pacto-relatorio>
	</div>
</adm-layout>
<pacto-traducoes-xingling #traducao>
	<span i18n="@@mensagem-sem-permissao" xingling="mensagem-sem-permissao">
		Seu usuário não possui permissão, procure seu administrador
	</span>
	<span i18n="@@servidor-facial:success-excluded" xingling="success-excluded">
		Servidor facial excluído com sucesso
	</span>
	<span i18n="@@adm:action-editar" xingling="action-editar">Editar</span>
	<span
		i18n="@@adm:tooltip-editar-servidor-facial"
		xingling="tooltip-editar-servidor-facial">
		Editar Servidor facial
	</span>
	<span i18n="@@adm:action-excluir" xingling="action-excluir">Excluir</span>
	<span
		i18n="@@adm:tooltip-excluir-servidor-facial"
		xingling="tooltip-excluir-servidor-facial">
		Excluir Servidor facial
	</span>
	<span
		i18n="@@servidor-facial:table-column-acoes"
		xingling="table-column-acoes">
		Ações
	</span>
	<span
		i18n="@@servidor-facial:msg-usuario-sem-permissao"
		xingling="msg-usuario-sem-permissao">
		Seu usuário não possui a permissão: 1.15 - Servidor Facial, procure o
		administrador.
	</span>
</pacto-traducoes-xingling>

<ng-template #columnCodigo>
	<span i18n="@@servidor-facial:column-codigo">Código</span>
</ng-template>
<ng-template #columnDescricao>
	<span i18n="@@servidor-facial:column-descricao">Descrição</span>
</ng-template>
<ng-template #columnEmpresa>
	<span i18n="@@servidor-facial:column-empresa">Empresa</span>
</ng-template>
