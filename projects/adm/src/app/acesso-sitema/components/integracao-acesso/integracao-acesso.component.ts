import {
	ChangeDetectorRef,
	Component,
	OnInit,
	TemplateRef,
	ViewChild,
} from "@angular/core";
import { Router } from "@angular/router";
import {
	AcessoSistemaApiIntegracaoAcessoService,
	ApiResponseList,
	IntegracaoAcessoGrupoEmpresarial,
} from "acesso-sistema-api";
import { GridFilterType } from "projects/ui/src/public-api";
import { SnotifyService } from "ng-snotify";
import { SessionService } from "sdk";
import {
	GridFilterConfig,
	PactoDataGridConfig,
	RelatorioComponent,
	TraducoesXinglingComponent,
} from "ui-kit";
import { map } from "rxjs/operators";
import { TreinoApiEmpresaService } from "treino-api";
import { Observable } from "rxjs";
import { AdmRestService } from "../../../adm-rest.service";

@Component({
	selector: "adm-integracao-acesso",
	templateUrl: "./integracao-acesso.component.html",
	styleUrls: ["./integracao-acesso.component.scss"],
})
export class IntegracaoAcessoComponent implements OnInit {
	@ViewChild("traducao", { static: true }) traducao: TraducoesXinglingComponent;
	@ViewChild("columnCodigo", { static: true }) columnCodigo: TemplateRef<any>;
	@ViewChild("columnDescricao", { static: true })
	columnDescricao: TemplateRef<any>;
	@ViewChild("columnEmpresaRemota", { static: true })
	columnEmpresaRemota: TemplateRef<any>;
	@ViewChild("tableData", { static: false }) tableData: RelatorioComponent;
	tableIntegracaoAcesso: PactoDataGridConfig;
	filterConfig: GridFilterConfig = { filters: [] };
	integracaoAcesso: ApiResponseList<Array<IntegracaoAcessoGrupoEmpresarial>> =
		{} as ApiResponseList<Array<IntegracaoAcessoGrupoEmpresarial>>;
	empresasList!: any;
	ready = false;

	constructor(
		private router: Router,
		private cd: ChangeDetectorRef,
		private session: SessionService,
		private admRest: AdmRestService,
		private notificationService: SnotifyService,
		private integracaoAcessoService: AcessoSistemaApiIntegracaoAcessoService,
		private empresaService: TreinoApiEmpresaService
	) {}

	ngOnInit() {
		this.initTableIntegracaoAcesso();
		this.initFilter();
		this.ready = true;
		this.getEmpresas().subscribe(() => {
			this.initFilter();
			this.ready = true;
		});
	}

	private getEmpresas(): Observable<any> {
		const empresas$ = this.empresaService.obterTodasEmpresas().pipe(
			map((data) => {
				data
					.sort((a, b) => (a.nome > b.nome ? 1 : -1))
					.forEach((empresa: any) => {
						empresa.value = empresa.id;
						empresa.label = empresa.nome.toUpperCase();
					});
				this.empresasList = data;
				return true;
			})
		);
		return empresas$;
	}

	private initTableIntegracaoAcesso() {
		this.tableIntegracaoAcesso = new PactoDataGridConfig({
			endpointUrl: this.admRest.buildFullUrlAcessoSistema("integracaoAcesso"),
			quickSearch: true,
			ghostLoad: true,
			ghostAmount: 5,
			showFilters: true,
			columns: [
				{
					nome: "codigo",
					titulo: this.columnCodigo,
					visible: true,
					defaultVisible: true,
					ordenavel: true,
				},
				{
					nome: "descricao",
					titulo: this.columnDescricao,
					visible: true,
					defaultVisible: true,
					ordenavel: true,
				},
				{
					nome: "nomeEmpresa",
					titulo: this.columnEmpresaRemota,
					visible: true,
					defaultVisible: true,
					ordenavel: true,
				},
			],
			actions: [
				{
					nome: this.traducao.getLabel("action-edit"),
					iconClass: "pct pct-edit cor-action-default-able04",
					tooltipText: this.traducao.getLabel(
						"tooltip-editar-integracao-acesso"
					),
					actionFn: (row) => this.editIntegracaoAcesso(row),
				},
				{
					nome: this.traducao.getLabel("action-delete"),
					iconClass: "pct pct-trash-2 cor-action-default-risk04",
					tooltipText: this.traducao.getLabel(
						"tooltip-excluir-integracao-acesso"
					),
					actionFn: (row) => this.editIntegracaoAcesso(row),
				},
			],
		});
	}

	private initFilter() {
		if (!this.tableIntegracaoAcesso) {
			this.tableIntegracaoAcesso = new PactoDataGridConfig({
				showFilters: true,
			});
		}
		this.filterConfig = {
			filters: [
				{
					name: "empresa",
					label: "Empresa",
					type: GridFilterType.DS3_SELECT_ONE,
					options: this.empresasList,
					initialValue: Number(this.session.empresaId),
				},
			],
		};
	}

	editIntegracaoAcesso(integracaoAcesso) {
		this.router.navigate([
			"adm",
			"acesso-sistema",
			"integracao-acesso",
			integracaoAcesso.codigo,
		]);
	}

	deleteIntegracaoAcesso(row: any) {
		this.integracaoAcessoService.delete(row.codigo).subscribe(
			(response) => {
				this.notificationService.success(
					this.traducao.getLabel("success-excluded")
				);
				this.tableData.reloadData();
			},
			(httpErrorResponse) => {
				const err = httpErrorResponse.error;
				if (err.meta && err.meta.messageValue) {
					this.notificationService.error(err.meta.messageValue);
				}
			}
		);
	}

	voltarHome() {
		this.router.navigate(["adm"]);
	}

	novaIntegracaoAcesso() {
		this.router.navigate(["adm", "acesso-sistema", "nova-integracao-acesso"]);
	}

	iconClickFn(event: { row: any; iconName: string }) {
		if (event.iconName === "action-edit (key)") {
			this.editIntegracaoAcesso(event.row);
		} else if (event.iconName === "action-delete (key)") {
			this.deleteIntegracaoAcesso(event.row);
		}
	}
}
