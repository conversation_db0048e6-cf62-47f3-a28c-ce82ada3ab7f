import { Component, Input, OnInit, ViewChild } from "@angular/core";
import {
	AbstractControl,
	FormControl,
	FormGroup,
	Validators,
} from "@angular/forms";
import { NgbActiveModal } from "@ng-bootstrap/ng-bootstrap";
import { CadastroAuxApiServidorFacialService, Camera } from "cadastro-aux-api";
import { SnotifyService } from "ng-snotify";

import { SelectFilterParamBuilder, TraducoesXinglingComponent } from "ui-kit";
import { AdmRestService } from "../../../adm-rest.service";

@Component({
	selector: "adm-servidor-facial-camera-modal",
	templateUrl: "./servidor-facial-camera-modal.component.html",
	styleUrls: ["./servidor-facial-camera-modal.component.scss"],
})
export class ServidorFacialCameraModalComponent implements OnInit {
	@ViewChild("traducao", { static: true }) traducao: TraducoesXinglingComponent;
	formGroup: FormGroup = new FormGroup({
		descricao: new FormControl(null, [Validators.required]),
		terminalConcentrador: new FormControl(null, [Validators.required]),
		urlRtsp: new FormControl(),
		camera: new FormControl(null, [Validators.required]),
	});
	@Input() camera: Camera = new Camera();

	constructor(
		private activeModal: NgbActiveModal,
		public servidorFacialService: CadastroAuxApiServidorFacialService,
		public admRest: AdmRestService,
		private notificationService: SnotifyService
	) {}

	ngOnInit() {
		this.formGroup.patchValue({
			descricao: this.camera.descricao,
			urlRtsp: this.camera.urlRtsp,
			camera: this.camera.indice,
		});
		if (this.camera.terminal) {
			this.servidorFacialService
				.findColetorByTerminal(this.camera.terminal)
				.subscribe((response) => {
					this.formGroup.patchValue({
						terminalConcentrador: response.content,
					});
				});
		}
	}

	dismissModal() {
		this.activeModal.dismiss();
	}

	salvar() {
		if (this.validacoes()) {
			this.camera.descricao = this.formGroup.get("descricao").value;
			this.camera.terminal = this.formGroup.get(
				"terminalConcentrador"
			).value.numeroTerminal;
			this.camera.indice = Number(this.formGroup.get("camera").value);
			this.camera.endereco = this.formGroup.get(
				"terminalConcentrador"
			).value.localAcesso.nomeComputador;
			this.camera.urlRtsp = this.formGroup.get("urlRtsp").value;
			this.activeModal.close(this.camera);
		}
	}

	validacoes() {
		if (
			this.formGroup.get("descricao").value === undefined ||
			this.formGroup.get("descricao").value === "" ||
			this.formGroup.get("descricao").value === null
		) {
			this.notificationService.error(
				this.traducao.getLabel("msg-validacao-descricao")
			);
			return false;
		} else if (
			this.formGroup.get("terminalConcentrador").value === undefined ||
			this.formGroup.get("terminalConcentrador").value === null
		) {
			this.notificationService.error(
				this.traducao.getLabel("msg-validacao-terminal")
			);
			return false;
		} else if (
			this.formGroup.get("camera").value === undefined ||
			this.formGroup.get("camera").value === "" ||
			this.formGroup.get("camera").value === null
		) {
			this.notificationService.error(
				this.traducao.getLabel("msg-validacao-camera")
			);
			return false;
		}
		return true;
	}

	toFormControl(absCtrl: AbstractControl | null): FormControl {
		const formControl = absCtrl as FormControl;
		return formControl;
	}

	selectBuilder: SelectFilterParamBuilder = (term) => {
		return {
			filters: JSON.stringify({
				quicksearchValue: term,
				quicksearchFields: ["nome"],
			}),
		};
	};

	verificaUsaRtsp() {
		if (
			this.formGroup.get("terminalConcentrador").value &&
			this.formGroup.get("terminalConcentrador").value !== undefined
		) {
			return this.formGroup.get("terminalConcentrador").value.usaRtsp;
		}
		return false;
	}
}
