<pacto-cat-card-plain>
	<div class="line-btn-close">
		<h1>Dados básicos</h1>
		<div (click)="dismissModal()"><i class="pct pct-x" id="btn-close"></i></div>
	</div>
	<div class="row">
		<div class="col-md-12">
			<pacto-cat-form-input
				[control]="toFormControl(formGroup.get('descricao'))"
				errorMsg="Forneça uma descrição para a câmera."
				i18n-errorMsg="@@servidor-facial-camera:error-msg-descricao"
				i18n-label="@@servidor-facial-cameral-modal:label-descricao"
				label="Descrição"></pacto-cat-form-input>
		</div>
	</div>
	<div class="row">
		<div class="col-md-12">
			<pacto-cat-form-select-filter
				[addEmptyOption]="false"
				[control]="toFormControl(formGroup.get('terminalConcentrador'))"
				[endpointUrl]="this.admRest.buildFullUrlCadAux('coletores/cod-name')"
				[paramBuilder]="selectBuilder"
				i18n-label="@@servidor-facial-cameral-modal:label-terminal"
				idKey="codigo"
				label="Terminal - concentrador"
				labelKey="descricao"></pacto-cat-form-select-filter>
		</div>
	</div>
	<div class="row">
		<div class="col-md-12">
			<pacto-cat-form-input
				*ngIf="this.verificaUsaRtsp()"
				[control]="toFormControl(formGroup.get('urlRtsp'))"
				i18n-label="@@servidor-facial-cameral-modal:label-urlRtsp"
				label="Url Rtsp"></pacto-cat-form-input>
		</div>
	</div>
	<div class="row">
		<div class="col-md-12">
			<pacto-cat-form-input-number
				[formControl]="toFormControl(formGroup.get('camera'))"
				errorMsg="Forneça o número da câmera."
				i18n-errorMsg="@@servidor-facial-camera:error-msg-camera"
				i18n-label="@@servidor-facial-cameral-modal:label-camera"
				label="Câmera"
				maxlength="3"></pacto-cat-form-input-number>
		</div>
	</div>

	<div class="row justify-content-end">
		<pacto-cat-button
			(click)="salvar()"
			[icon]="'pct pct-save'"
			i18n-label="@@conta-corrente:btn-salvar"
			label="Salvar"
			size="LARGE"
			style="margin-right: 10px"
			type="PRIMARY"
			width="112px"></pacto-cat-button>
	</div>
</pacto-cat-card-plain>

<pacto-traducoes-xingling #traducao>
	<span
		i18n="@@servidor-facial-camera:msg-validacao-descricao"
		xingling="msg-validacao-descricao">
		O campo "Descrição" deve ser informado."
	</span>
	<span
		i18n="@@servidor-facial-camera:msg-validacao-terminal"
		xingling="msg-validacao-terminal">
		O campo "Terminal - concentrador" deve ser informado."
	</span>
	<span
		i18n="@@servidor-facial-camera:msg-validacao-camera"
		xingling="msg-validacao-camera">
		O campo "Câmera" deve ser informado."
	</span>
</pacto-traducoes-xingling>
