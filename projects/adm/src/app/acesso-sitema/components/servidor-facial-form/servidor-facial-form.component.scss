@import "projects/ui/assets/import.scss";

.tabela {
	display: flex;
}

.coluna-1 {
	flex: 50%;
}

.coluna-2 {
	flex: 50%;
	margin-left: 100px;
}

#table-cameras {
	padding-left: 15px;
}

pacto-cat-form-input,
pacto-cat-form-input-number,
pacto-cat-form-select-filter {
	margin: 0px;
}

.titulo {
	position: static;
	width: 311px;
	height: 42px;
	left: 0px;
	top: 0px;

	font-family: Nunito Sans;
	font-style: normal;
	font-weight: bold;
	font-size: 24px;
	line-height: 16px;
	/* identical to box height, or 67% */

	display: flex;
	align-items: center;
	color: #51555a;
}

#informe-os-dados {
	width: 100%;
	height: 88px;
	margin: 0px 16px 32px 16px;
	background: #f7f7f7;
	border: 1px solid #d3d5d7;
	box-sizing: border-box;
	border-radius: 8px;
	margin-top: 36px;
}

#informe-os-dados h1,
#informe-os-dados p {
	font-family: "Nunito Sans";
	font-style: normal;
	font-size: 14px;
	line-height: 16px;
	align-items: center;
	margin-left: 16px;
	margin-right: 16px;
}

#informe-os-dados h1 {
	margin-top: 16px;
	font-weight: 600;
	color: #000000;
}

#informe-os-dados p {
	margin-top: 8px;
	color: #6f747b;
}

.add-camera {
	cursor: pointer;
	margin-top: -15px;

	span {
		margin-left: 4px;
	}
}
