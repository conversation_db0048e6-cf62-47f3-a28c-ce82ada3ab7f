import {
	AfterViewInit,
	ChangeDetectorRef,
	Component,
	OnInit,
	TemplateRef,
	ViewChild,
} from "@angular/core";
import {
	AbstractControl,
	FormControl,
	FormGroup,
	Validators,
} from "@angular/forms";
import { ActivatedRoute, Router } from "@angular/router";
import { NgbModal } from "@ng-bootstrap/ng-bootstrap";

import {
	CadastroAuxApiEmpresaService,
	CadastroAuxApiServidorFacialService,
	Camera,
	ServidorFacial,
} from "cadastro-aux-api";
import { SnotifyService } from "ng-snotify";
import { SessionService } from "sdk";

import {
	PactoDataGridConfig,
	PactoDataGridState,
	RelatorioComponent,
	SelectFilterParamBuilder,
	TableData,
	TraducoesXinglingComponent,
} from "ui-kit";
import { AdmRestService } from "../../../adm-rest.service";
import { ServidorFacialCameraModalComponent } from "../servidor-facil-camera-modal/servidor-facial-camera-modal.component";

@Component({
	selector: "adm-servidor-facial-form",
	templateUrl: "./servidor-facial-form.component.html",
	styleUrls: ["./servidor-facial-form.component.scss"],
})
export class ServidorFacialFormComponent implements OnInit, AfterViewInit {
	@ViewChild("traducao", { static: true }) traducao: TraducoesXinglingComponent;
	@ViewChild("columnCodigo", { static: true }) columnCodigo: TemplateRef<any>;
	@ViewChild("columnDescricao", { static: true })
	columnDescricao: TemplateRef<any>;
	@ViewChild("tableCamerasComponent", { static: true })
	tableCamerasComponent: RelatorioComponent;
	formGroup: FormGroup = new FormGroup({
		empresa: new FormControl(),
		descricao: new FormControl("", Validators.required),
		codigo: new FormControl(),
		servidorBdFacial: new FormControl(""),
		nomeComputador: new FormControl("", Validators.required),
		falsoPositivo: new FormControl(0.5, Validators.required),
		tempoMinimoReenvio: new FormControl(5000),
		distanciaIdentificacao: new FormControl(0, [
			Validators.min(80),
			Validators.max(500),
		]),
	});
	id;
	tableData: PactoDataGridConfig;
	servidorFacial = new ServidorFacial();
	camerasData: {
		content: Array<Camera>;
		first: boolean;
		last: boolean;
		number: number;
		size: number;
		totalElements: number;
		totalPages: number;
	} = {
		content: new Array<Camera>(),
		first: true,
		last: false,
		number: 0,
		size: 0,
		totalElements: 0,
		totalPages: 0,
	};
	page = 1;
	size = 5;
	itensPerPage = [
		{ id: 5, label: "5" },
		{ id: 10, label: "10" },
		{ id: 15, label: "15" },
	];
	state: PactoDataGridState = new PactoDataGridState();

	constructor(
		private route: Router,
		private activatedRoute: ActivatedRoute,
		private notificationService: SnotifyService,
		public servidorFacialService: CadastroAuxApiServidorFacialService,
		private cd: ChangeDetectorRef,
		private sessionService: SessionService,
		public cadastroAuxApiEmpresaService: CadastroAuxApiEmpresaService,
		private ngbModal: NgbModal,
		public admRestService: AdmRestService
	) {}

	ngOnInit() {
		this.initForms();
		this.state.paginaTamanho = 5;
		this.state.paginaNumero = 0;
		this.initTableCameras();
	}

	ngAfterViewInit() {
		this.id = this.activatedRoute.snapshot.paramMap.get("id");
		if (this.id > 0) {
			this.servidorFacialService.find(this.id).subscribe((response) => {
				this.servidorFacial = response.content;
				this.formGroup.patchValue({
					empresa: this.servidorFacial.empresa,
					descricao: this.servidorFacial.descricao,
					codigo: this.servidorFacial.codigo,
					servidorBdFacial: this.servidorFacial.servidorBdFacial,
					nomeComputador: this.servidorFacial.nomeComputador,
					falsoPositivo: this.servidorFacial.falsoPositivo,
					tempoMinimoReenvio: this.servidorFacial.tempoMinimoReenvio,
					distanciaIdentificacao: this.servidorFacial.distanciaIdentificacao,
				});
				this.createPageObject();
			});
		} else {
			this.cadastroAuxApiEmpresaService
				.findById(this.sessionService.empresaId)
				.subscribe((response) => {
					this.formGroup.patchValue({
						empresa: response.content,
					});
				});
		}
		this.tableData.dataAdapterFn = (serverData): TableData<any> => {
			serverData = this.camerasData;
			return serverData;
		};
		this.cd.detectChanges();
	}

	initForms() {
		this.formGroup.get("codigo").disable();
		if (!this.sessionService.loggedUser.administrador) {
			this.formGroup.get("empresa").disable();
		}
	}

	initTableCameras() {
		this.tableData = new PactoDataGridConfig({
			pagination: true,
			state: this.state,
			formGroup: new FormGroup({
				codigo: new FormControl(),
				descricao: new FormControl(),
			}),
			columns: [
				{
					nome: "codigo",
					titulo: this.columnCodigo,
					visible: false,
					ordenavel: false,
				},
				{
					nome: "descricao",
					titulo: this.columnDescricao,
					visible: true,
					ordenavel: true,
				},
			],
			actions: [
				{
					nome: this.traducao.getLabel("action-edit"),
					iconClass: "pct pct-edit cor-action-default-able04",
					tooltipText: this.traducao.getLabel("tooltip-editar-camera"),
					actionFn: (row) => this.openModalEdit(row),
				},
				{
					nome: this.traducao.getLabel("action-delete"),
					iconClass: "pct pct-trash-2 cor-action-default-risk04",
					tooltipText: this.traducao.getLabel("tooltip-excluir-camera"),
					actionFn: (row) => this.delete(row),
				},
			],
		});
		this.tableCamerasComponent.ngbPage = 1;
	}

	voltarParaListagem() {
		this.route.navigate(["adm", "acesso-sistema", "servidor-facial"]);
	}

	salvar() {
		if (
			this.sessionService.perfilUsuario.funcionalidades.get("servidorfacial")
		) {
			Object.keys(this.formGroup.getRawValue()).forEach((key) => {
				this.servidorFacial[key] = this.formGroup.getRawValue()[key];
			});
			this.servidorFacialService.save(this.servidorFacial).subscribe(
				(response) => {
					this.notificationService.success(
						this.traducao.getLabel("saved-success")
					);
					this.voltarParaListagem();
				},
				(httpErrorResponse) => {
					const err = httpErrorResponse.error;
					if (err.meta && err.meta.messageValue) {
						this.notificationService.error(err.meta.messageValue);
					}
				}
			);
		} else {
			this.notificationService.warning(
				this.traducao.getLabel("msg-usuario-sem-permissao")
			);
		}
	}

	selectBuilder: SelectFilterParamBuilder = (term) => {
		return {
			filters: JSON.stringify({
				quicksearchValue: term,
				quicksearchFields: ["nome"],
			}),
		};
	};

	toFormControl(absCtrl: AbstractControl | null): FormControl {
		const formControl = absCtrl as FormControl;
		return formControl;
	}

	openModalAdd() {
		const dialogRef = this.ngbModal.open(ServidorFacialCameraModalComponent, {
			windowClass: "modal-camera",
		});
		if (dialogRef.result) {
			dialogRef.result
				.then((camera) => {
					this.servidorFacial.cameras.push(camera);
					this.createPageObject();
				})
				.catch((error) => {});
		}
		this.cd.detectChanges();
	}

	openModalEdit(row) {
		const dialogRef = this.ngbModal.open(ServidorFacialCameraModalComponent, {
			windowClass: "modal-camera",
		});
		if (dialogRef.result) {
			dialogRef.result.then((camera) => {}).catch((error) => {});
		}
		dialogRef.componentInstance.camera = row;
		this.cd.detectChanges();
	}

	delete(row) {
		const indexExcluir = this.servidorFacial.cameras.findIndex(
			(c) => c.codigo === row.codigo
		);
		if (indexExcluir >= 0) {
			this.servidorFacial.cameras.splice(indexExcluir, 1);
		}
		this.camerasData.content.splice(row.rowIndex, 1);
		this.createPageObject();
	}

	pageChangeEvent(page) {
		if (!isNaN(page)) {
			this.page = page;
		}
		this.createPageObject(this.page, this.size);
	}

	pageSizeChange(size) {
		if (!isNaN(size)) {
			this.size = size;
			this.page = 1;
		}
		this.createPageObject(this.page, this.size);
	}

	createPageObject(page = 1, size = 5) {
		this.camerasData.totalElements = this.servidorFacial.cameras.length;
		this.camerasData.size = size;
		this.camerasData.totalPages = Math.ceil(
			+(this.camerasData.totalElements / this.camerasData.size)
		);
		this.camerasData.first = page === 0 || page === 1;
		this.camerasData.last = page === this.camerasData.totalPages;
		this.camerasData.content = this.servidorFacial.cameras.slice(
			size * page - size,
			size * page
		);
		this.tableCamerasComponent.showBtnAdd = false;
		this.tableCamerasComponent.ngbPage = this.page;
		this.tableCamerasComponent.reloadData();
		this.cd.detectChanges();
	}

	sortEvent(sortEvent: { columnName: any; direction: any; column: any }) {
		this.servidorFacial.cameras = this.servidorFacial.cameras.sort((a, b) => {
			if (sortEvent.direction === "ASC") {
				if (a.descricao > b.descricao) {
					return 1;
				} else if (a.descricao < b.descricao) {
					return -1;
				} else {
					return 0;
				}
			} else {
				if (a.descricao < b.descricao) {
					return 1;
				} else if (a.descricao > b.descricao) {
					return -1;
				} else {
					return 0;
				}
			}
		});
		this.createPageObject();
	}
}
