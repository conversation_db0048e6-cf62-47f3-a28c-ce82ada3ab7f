<adm-layout
	(goBack)="voltarParaListagem()"
	i18n-modulo="@@servidor-facial:modulo"
	i18n-pageTitle="@@servidor-facial:title"
	modulo="Administrativo"
	pageTitle="Servidor facial">
	<pacto-cat-card-plain>
		<div class="table-wrapper pacto-shadow">
			<div class="tabela">
				<div class="coluna-1">
					<h1 class="titulo" i18n="@@servidor-facial:insira-os-dados">
						Insira os dados
					</h1>
					<div class="row">
						<div class="col-md-12">
							<pacto-cat-form-select-filter
								[addEmptyOption]="true"
								[control]="toFormControl(formGroup.get('empresa'))"
								[endpointUrl]="
									this.admRestService.buildFullUrlCadAux('empresas')
								"
								[paramBuilder]="selectBuilder"
								i18n-label="@@relatorio-visistantes:label-unidade"
								idKey="codigo"
								label="Empresa"
								labelKey="nome"></pacto-cat-form-select-filter>
						</div>
					</div>
					<div class="row">
						<div class="col-md-7" style="margin-left: 0px">
							<pacto-cat-form-input
								[control]="toFormControl(formGroup.get('descricao'))"
								errorMsg="Forneça uma descriçao para o servidor facial"
								i18n-errorMsg="@@servidor-facial:error-msg-descricao"
								i18n-label="@@servidor-facial:label-descricao"
								label="Descrição"></pacto-cat-form-input>
						</div>
						<div class="col-md-5" style="margin-left: 0px">
							<pacto-cat-form-input-number
								[formControl]="toFormControl(formGroup.get('codigo'))"
								i18n-label="@@servidor-facial:label-codigo"
								label="Código"></pacto-cat-form-input-number>
						</div>
					</div>
					<div class="row">
						<div class="col-md-7" style="margin-left: 0px">
							<pacto-cat-form-input
								[control]="toFormControl(formGroup.get('servidorBdFacial'))"
								i18n-label="@@servidor-facial:label-servidor-bd-facial"
								label="Servidor de banco de dados facial"></pacto-cat-form-input>
						</div>
						<div class="col-md-5" style="margin-left: 0px">
							<pacto-cat-form-input
								[control]="toFormControl(formGroup.get('nomeComputador'))"
								errorMsg="Forneça o nome do Concentrador de câmeras"
								i18n-errorMsg="@@servidor-facial:error-msg-concentrador-cameras"
								i18n-label="@@servidor-facial:label-concentrador-cameras"
								label="Concentrador de câmeras"></pacto-cat-form-input>
						</div>
					</div>
					<div class="row">
						<div class="col-md-7" style="margin-left: 0px">
							<pacto-cat-form-input-number
								[formControl]="toFormControl(formGroup.get('falsoPositivo'))"
								decimal="true"
								i18n-label="@@servidor-facial:label-falso-positivo"
								label="Falso positivo"
								maxlength="2"></pacto-cat-form-input-number>
						</div>
						<div class="col-md-5" style="margin-left: 0px">
							<pacto-cat-form-input-number
								[formControl]="
									toFormControl(formGroup.get('tempoMinimoReenvio'))
								"
								i18n-label="@@servidor-facial:label-tempo-minimo-acesso"
								label="Tempo mínimo de acesso"
								maxlength="8"></pacto-cat-form-input-number>
						</div>
					</div>
					<div class="row">
						<div class="col-md-7" style="margin-left: 0px; margin-bottom: 20px">
							<pacto-cat-form-input-number
								[formControl]="
									toFormControl(formGroup.get('distanciaIdentificacao'))
								"
								errorMsg="O valor mínimo deste campo é 80 e máximo 500"
								i18n-errorMsg="
									@@servidor-facial:error-msg-distanciaIdentificacao"
								i18n-label="@@servidor-facial:distancia-identificacao"
								label="Distância de Identificação"
								maxlength="3"></pacto-cat-form-input-number>
						</div>
					</div>
					<div class="row">
						<div class="col-md-12 d-flex align-items-center">
							<div (click)="openModalAdd()" class="add-camera cor-azulim-pri">
								<i class="pct pct-plus-circle"></i>
								<span i18n="@@servidor-facial:camera-adicionar-novo">
									Adicionar novo
								</span>
							</div>
						</div>
					</div>
				</div>
				<div class="coluna-2">
					<h1 class="titulo" i18n="@@servidor-facial:cameras">Câmeras</h1>
					<div id="table-cameras">
						<pacto-cat-table-editable
							#tableCamerasComponent
							(delete)="delete($event)"
							(pageChangeEvent)="pageChangeEvent($event)"
							(pageSizeChange)="pageSizeChange($event)"
							(sortEvent)="sortEvent($event)"
							[actionTitle]="traducao.getLabel('table-column-acoes')"
							[itensPerPage]="itensPerPage"
							[table]="tableData"></pacto-cat-table-editable>
					</div>
				</div>
			</div>
		</div>
		<div class="row">
			<div id="informe-os-dados">
				<h1 i18n="@@servidor-facial:informe-os-dados">Informe os dados</h1>
				<p i18n="@@servidor-facial:informe-os-paramentros">
					Informe os parâmetros acima para cadastrar um novo Servidor facial.
				</p>
			</div>
		</div>
		<div class="row justify-content-end">
			<pacto-cat-button
				(click)="voltarParaListagem()"
				i18n-label="@@servidor-facial:btn-voltar"
				label="Voltar"
				size="LARGE"
				style="margin-right: 10px"
				type="OUTLINE"
				width="112px"></pacto-cat-button>
			<pacto-cat-button
				(click)="salvar()"
				[icon]="'pct pct-save'"
				i18n-label="@@servidor-facial:btn-salvar"
				label="Salvar"
				size="LARGE"
				style="margin-right: 10px"
				type="PRIMARY"
				width="112px"></pacto-cat-button>
		</div>
	</pacto-cat-card-plain>
</adm-layout>

<pacto-traducoes-xingling #traducao>
	<span i18n="@@servidor-facial:saved-success" xingling="saved-success">
		Servidor facial salvo com sucesso!
	</span>
	<span
		i18n="@@servidor-facial:table-column-acoes"
		xingling="table-column-acoes">
		Ações
	</span>
	<span
		i18n="@@servidor-facial:tooltip-editar-camera"
		xingling="tooltip-editar-camera">
		Editar câmera
	</span>
	<span
		i18n="@@servidor-facial:tooltip-excluir-camera"
		xingling="tooltip-excluir-camera">
		Excluir câmera
	</span>
	<span
		i18n="@@servidor-facial:msg-usuario-sem-permissao"
		xingling="msg-usuario-sem-permissao">
		Seu usuário não possui a permissão: 1.15 - Servidor Facial, procure o
		administrador.
	</span>
</pacto-traducoes-xingling>

<ng-template #columnCodigo>
	<span i18n="@@servidor-facial:column-codigo">Código</span>
</ng-template>
<ng-template #columnDescricao>
	<span i18n="@@servidor-facial:column-descricao-camera">Descrição</span>
</ng-template>
