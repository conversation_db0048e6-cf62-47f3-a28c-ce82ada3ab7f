<adm-layout
	(goBack)="voltarTelaIntegracaoAcesso()"
	i18n-modulo="@@integracao-acesso:modulo"
	i18n-pageTitle="@@integracao-acesso:title"
	modulo="Administrativo"
	pageTitle="Integração de acesso">
	<pacto-cat-card-plain>
		<div class="table-wrapper pacto-shadow">
			<h1 i18n="@@integracao-acesso:insira-os-dados">Insira os dados</h1>
			<div class="row">
				<div class="col-md-6">
					<pacto-cat-form-input
						[control]="formGroup.get('descricao')"
						[maxlength]="80"
						i18n-label="@@integracao-acesso:label-descricao"
						i18n-placeholder="@@integracao-acesso:placeholder-descricao"
						label="Descrição"
						placeholder="Descrição"></pacto-cat-form-input>
				</div>
				<div class="col-md-6">
					<pacto-cat-form-input
						[control]="formGroup.get('urlZillyonWeb')"
						i18n-label="@@integracao-acesso:label-url-unidade"
						i18n-placeholder="@@integracao-acesso:placeholder-url-unidade"
						label="URL da unidade"
						placeholder="URL da unidade"></pacto-cat-form-input>
				</div>
			</div>
			<div class="row">
				<div class="col-md-6">
					<pacto-cat-form-input
						[control]="formGroup.get('chave')"
						i18n-label="@@integracao-acesso:label-chave-empresa"
						i18n-placeholder="@@integracao-acesso:placeholder-chave-empresa"
						label="Chave da empresa"
						placeholder="Chave da empresa"></pacto-cat-form-input>
				</div>
				<div class="col-md-3">
					<ng-container *ngIf="loadingEmpresaRemota">
						<div class="loader">
							<img src="assets/images/loading.svg" />
							<span i18n="@@integracao-acesso:loading-message">
								Carregando empresa remota...
							</span>
						</div>
					</ng-container>
					<pacto-cat-form-select
						*ngIf="!loadingEmpresaRemota"
						[control]="formGroup.get('empresaRemota')"
						[items]="empresasRemotas"
						i18n-label="@@integracao-acesso:label-empresa-remota"
						idKey="codigo"
						label="Empresa remota"
						labelKey="nome"></pacto-cat-form-select>
				</div>
			</div>
			<div class="row">
				<div class="col-md-3">
					<ng-container *ngIf="loadingLocalAcesso">
						<div class="loader">
							<img src="assets/images/loading.svg" />
							<span i18n="@@integracao-acesso:loading-message">
								Carregando local de acesso...
							</span>
						</div>
					</ng-container>
					<pacto-cat-form-select
						*ngIf="!loadingLocalAcesso"
						[control]="formGroup.get('localAcesso')"
						[items]="locaisAcesso"
						i18n-label="@@integracao-acesso:label-local-acesso"
						idKey="codigo"
						label="Local de acesso"
						labelKey="descricao"></pacto-cat-form-select>
				</div>
				<div class="col-md-3">
					<pacto-cat-form-select
						[control]="formGroup.get('coletor')"
						[items]="coletores"
						i18n-label="@@integracao-acesso:label-coletor"
						idKey="codigo"
						label="Coletor"
						labelKey="descricao"></pacto-cat-form-select>
				</div>
				<div class="col-md-3">
					<pacto-cat-form-input
						[control]="formGroup.get('terminal')"
						i18n-label="@@integracao-acesso:label-terminal"
						label="Terminal"
						placeholder="000"
						readonly="true"></pacto-cat-form-input>
				</div>
			</div>
		</div>
		<div class="row justify-content-end">
			<pacto-cat-button
				(click)="voltarTelaIntegracaoAcesso()"
				i18n-label="@@integracao-acesso:btn-voltar"
				label="Voltar"
				style="margin-right: 10px"
				type="OUTLINE_DARK"></pacto-cat-button>
			<pacto-cat-button
				(click)="salvarIntegracaoAcesso()"
				[icon]="'pct pct-save'"
				i18n-label="@@integracao-acesso:btn-salvar"
				label="Salvar"
				style="margin-right: 10px"
				type="PRIMARY"></pacto-cat-button>
		</div>
	</pacto-cat-card-plain>
</adm-layout>

<pacto-traducoes-xingling #traducao>
	<span i18n="@@integracao-acesso:saved-success" xingling="saved-success">
		Integração de acesso salva com sucesso!
	</span>
	<span
		i18n="@@integracao-acesso:mesma-chave-empresa"
		xingling="mesma-chave-empresa">
		A chave não pode ser a mesma utilizada por essa empresa!
	</span>
	<span
		i18n="@@integracao-acesso:empresa-remota-encontrada"
		xingling="empresa-remota-encontrada">
		Empresa remota encontrada!
	</span>
</pacto-traducoes-xingling>
