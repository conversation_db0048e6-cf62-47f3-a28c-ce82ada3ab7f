import {
	AfterViewInit,
	ChangeDetectorRef,
	Component,
	OnInit,
	ViewChild,
} from "@angular/core";
import { FormControl, FormGroup } from "@angular/forms";
import { ActivatedRoute, Router } from "@angular/router";
import {
	AcessoSistemaApiIntegracaoAcessoService,
	Coletor,
	IntegracaoAcessoEmpresa,
	IntegracaoAcessoGrupoEmpresarial,
	LocalAcesso,
} from "acesso-sistema-api";
import { SnotifyService } from "ng-snotify";
import { debounceTime } from "rxjs/operators";
import { SessionService } from "sdk";
import { TraducoesXinglingComponent } from "ui-kit";
import { PerfilAcessoRecursoNome } from "../../../perfil-acesso/perfil-acesso-recurso.model";

@Component({
	selector: "adm-integracao-acesso-form",
	templateUrl: "./integracao-acesso-form.component.html",
	styleUrls: ["./integracao-acesso-form.component.scss"],
})
export class IntegracaoAcessoFormComponent implements OnInit, AfterViewInit {
	@ViewChild("traducao", { static: true }) traducao: TraducoesXinglingComponent;
	formGroup: FormGroup = new FormGroup({
		descricao: new FormControl(),
		urlZillyonWeb: new FormControl(),
		chave: new FormControl(),
		empresaRemota: new FormControl(),
		localAcesso: new FormControl(),
		coletor: new FormControl(),
		terminal: new FormControl(),
	});
	integracaoAcesssoGrupoEmpresarial = new IntegracaoAcessoGrupoEmpresarial();
	id;
	empresasRemotas = new Array<IntegracaoAcessoEmpresa>();
	locaisAcesso = new Array<LocalAcesso>();
	coletores = new Array<Coletor>();
	loadingEmpresaRemota;
	loadingLocalAcesso;

	constructor(
		private route: Router,
		private activatedRoute: ActivatedRoute,
		private notificationService: SnotifyService,
		private integracaoAcessoService: AcessoSistemaApiIntegracaoAcessoService,
		private cd: ChangeDetectorRef,
		private sessionService: SessionService
	) {}

	ngOnInit() {
		this.id = this.activatedRoute.snapshot.paramMap.get("id");
		this.formGroup
			.get("chave")
			.valueChanges.pipe(debounceTime(500))
			.subscribe((key) => {
				if (key !== undefined && !key.isEmpty) {
					if (
						key.trim().toLowerCase() !==
						this.sessionService.chave.trim().toLowerCase()
					) {
						this.loadEmpresasRemotas(key);
					} else {
						this.limparSelectEmpresaRemota();
						this.notificationService.error(
							this.traducao.getLabel("mesma-chave-empresa")
						);
					}
				}
			});
		this.formGroup
			.get("empresaRemota")
			.valueChanges.pipe(debounceTime(300))
			.subscribe((e) => {
				this.loadLocaisAcessoRemotos();
			});
		this.formGroup.get("localAcesso").valueChanges.subscribe((l) => {
			this.loadColetores();
		});
		this.formGroup.get("coletor").valueChanges.subscribe((c) => {
			this.setNumeroTerminal();
		});
	}

	carregarRecurso() {
		return this.sessionService.recursos.get(PerfilAcessoRecursoNome.PLANO);
	}

	ngAfterViewInit() {
		if (this.id > 0) {
			this.integracaoAcessoService.find(this.id).subscribe((response) => {
				this.integracaoAcesssoGrupoEmpresarial = response.content;
				this.formGroup.patchValue({
					descricao: this.integracaoAcesssoGrupoEmpresarial.descricao,
					urlZillyonWeb: this.integracaoAcesssoGrupoEmpresarial.urlZillyonWeb,
					chave: this.integracaoAcesssoGrupoEmpresarial.chave,
				});
			});
		}
		this.cd.detectChanges();
	}

	voltarTelaIntegracaoAcesso() {
		this.route.navigate(["adm", "acesso-sistema", "integracao-acesso"]);
	}

	salvarIntegracaoAcesso() {
		Object.keys(this.formGroup.getRawValue()).forEach((key) => {
			this.integracaoAcesssoGrupoEmpresarial[key] =
				this.formGroup.getRawValue()[key];
		});
		if (
			this.integracaoAcesssoGrupoEmpresarial.nomeEmpresa === undefined ||
			this.integracaoAcesssoGrupoEmpresarial.nomeEmpresa === null ||
			this.integracaoAcesssoGrupoEmpresarial.nomeEmpresa === ""
		) {
			const idx = this.empresasRemotas.findIndex(
				(e) =>
					e.codigo ===
					Number(this.integracaoAcesssoGrupoEmpresarial.empresaRemota)
			);
			if (idx >= 0) {
				this.integracaoAcesssoGrupoEmpresarial.nomeEmpresa =
					this.empresasRemotas[idx].nome;
			}
		}
		if (
			this.integracaoAcesssoGrupoEmpresarial.empresaLocal === undefined ||
			this.integracaoAcesssoGrupoEmpresarial.empresaLocal === null
		) {
			const empresaLocal = new IntegracaoAcessoEmpresa();
			empresaLocal.codigo = Number(this.sessionService.empresaId);
			this.integracaoAcesssoGrupoEmpresarial.empresaLocal = empresaLocal;
		}
		this.integracaoAcessoService
			.save(this.integracaoAcesssoGrupoEmpresarial)
			.subscribe(
				(response) => {
					this.notificationService.success(
						this.traducao.getLabel("saved-success")
					);
					this.voltarTelaIntegracaoAcesso();
				},
				(httpErrorResponse) => {
					const err = httpErrorResponse.error;
					if (err.meta && err.meta.messageValue) {
						this.notificationService.error(err.meta.messageValue);
					}
				}
			);
	}

	loadColetores() {
		this.limparSelectColetor();
		const localAcesso = Number(this.formGroup.get("localAcesso").value);
		const index = this.locaisAcesso.findIndex((l) => l.codigo === localAcesso);
		if (index >= 0 && localAcesso > 0) {
			this.locaisAcesso[index].coletores.forEach((c) => {
				this.coletores.push(c);
			});
			if (
				this.id > 0 &&
				this.formGroup.get("chave").value ===
					this.integracaoAcesssoGrupoEmpresarial.chave &&
				this.formGroup.get("empresaRemota").value ===
					this.integracaoAcesssoGrupoEmpresarial.empresaRemota
			) {
				const idx = this.coletores.findIndex(
					(c) => c.codigo === this.integracaoAcesssoGrupoEmpresarial.coletor
				);
				if (idx >= 0) {
					this.formGroup.get("coletor").setValue(this.coletores[idx].codigo);
				}
			}
		}
		this.cd.detectChanges();
	}

	setNumeroTerminal() {
		const coletor = Number(this.formGroup.get("coletor").value);
		const index = this.coletores.findIndex((c) => c.codigo === coletor);
		if (index >= 0) {
			this.formGroup
				.get("terminal")
				.setValue(this.coletores[index].numeroTerminal);
		} else {
			this.formGroup.get("terminal").setValue(undefined);
		}
	}

	private loadEmpresasRemotas(key) {
		this.limparSelectEmpresaRemota();
		if (key !== undefined && key !== "" && key !== "null") {
			this.loadingEmpresaRemota = true;
			this.integracaoAcessoService.findAllEmpresasByKey(key).subscribe(
				(response) => {
					if (response != null) {
						response.content.forEach((r) => {
							this.empresasRemotas.push(r);
						});
						if (
							this.id > 0 &&
							key === this.integracaoAcesssoGrupoEmpresarial.chave
						) {
							const index = response.content.findIndex(
								(r) =>
									Number(r.codigo) ===
									Number(this.integracaoAcesssoGrupoEmpresarial.empresaRemota)
							);
							if (index >= 0) {
								this.formGroup
									.get("empresaRemota")
									.setValue(response.content[index].codigo);
							}
						}
					}
					this.loadingEmpresaRemota = false;
					this.cd.detectChanges();
				},
				(httpErrorResponse) => {
					const err = httpErrorResponse.error;
					if (err.meta && err.meta.messageValue) {
						this.notificationService.error(err.meta.messageValue);
					}
					this.loadingEmpresaRemota = false;
					this.cd.detectChanges();
				}
			);
		}
	}

	private loadLocaisAcessoRemotos() {
		this.limparSelectLocalAcesso();
		const chave = this.formGroup.get("chave").value;
		const empresaRemota = this.formGroup.get("empresaRemota").value;
		if (
			empresaRemota !== undefined &&
			empresaRemota > 0 &&
			chave !== undefined &&
			chave !== "" &&
			chave !== "null"
		) {
			this.loadingLocalAcesso = true;
			const filtros = { key: chave, empresa: empresaRemota };
			this.integracaoAcessoService.findAllLocaisAcesso(filtros).subscribe(
				(response) => {
					if (response != null) {
						response.content.forEach((r) => {
							this.locaisAcesso.push(r);
						});
						if (
							this.id > 0 &&
							chave === this.integracaoAcesssoGrupoEmpresarial.chave &&
							empresaRemota ===
								this.integracaoAcesssoGrupoEmpresarial.empresaRemota
						) {
							const index = response.content.findIndex(
								(r) =>
									Number(r.codigo) ===
									Number(this.integracaoAcesssoGrupoEmpresarial.localAcesso)
							);
							if (index >= 0) {
								this.formGroup
									.get("localAcesso")
									.setValue(response.content[index].codigo);
							}
						}
						this.loadingLocalAcesso = false;
						this.cd.detectChanges();
					}
				},
				(httpErrorResponse) => {
					const err = httpErrorResponse.error;
					if (err.meta && err.meta.messageValue) {
						this.notificationService.error(err.meta.messageValue);
					}
					this.loadingLocalAcesso = false;
					this.cd.detectChanges();
				}
			);
		}
	}

	private limparSelectEmpresaRemota() {
		this.formGroup.get("empresaRemota").setValue(0);
		this.empresasRemotas = new Array<IntegracaoAcessoEmpresa>();
		const empresa = new IntegracaoAcessoEmpresa();
		empresa.codigo = 0;
		empresa.nome = "-";
		this.empresasRemotas.push(empresa);
	}

	private limparSelectLocalAcesso() {
		this.formGroup.get("localAcesso").setValue(0);
		this.locaisAcesso = new Array<LocalAcesso>();
		const localAcesso = new LocalAcesso();
		localAcesso.codigo = 0;
		localAcesso.descricao = "-";
		this.locaisAcesso.push(localAcesso);
	}

	private limparSelectColetor() {
		this.coletores = new Array<Coletor>();
		const coletor = new Coletor();
		coletor.codigo = 0;
		coletor.descricao = "-";
		this.coletores.push(coletor);
		this.formGroup.get("coletor").setValue(0);
	}
}
