import { CommonModule } from "@angular/common";
import { NgModule } from "@angular/core";
import { NgbModule } from "@ng-bootstrap/ng-bootstrap";
import { SdkModule } from "sdk";
import { ComponentsModule, UiModule } from "ui-kit";
import { LayoutModule } from "../layout/layout.module";

import { AcessoSitemaRoutingModule } from "./acesso-sitema-routing.module";
import { IntegracaoAcessoFormComponent } from "./components/integracao-acesso-form/integracao-acesso-form.component";
import { IntegracaoAcessoComponent } from "./components/integracao-acesso/integracao-acesso.component";
import { ServidorFacialFormComponent } from "./components/servidor-facial-form/servidor-facial-form.component";
import { ServidorFacialComponent } from "./components/servidor-facial/servidor-facial.component";
import { ServidorFacialCameraModalComponent } from "./components/servidor-facil-camera-modal/servidor-facial-camera-modal.component";

@NgModule({
	declarations: [
		IntegracaoAcessoComponent,
		IntegracaoAcessoFormComponent,
		ServidorFacialComponent,
		ServidorFacialFormComponent,
		ServidorFacialCameraModalComponent,
	],
	imports: [
		CommonModule,
		AcessoSitemaRoutingModule,
		ComponentsModule,
		LayoutModule,
		UiModule,
		SdkModule,
		NgbModule,
	],
	providers: [],
	entryComponents: [ServidorFacialCameraModalComponent],
})
export class AcessoSitemaModule {}
