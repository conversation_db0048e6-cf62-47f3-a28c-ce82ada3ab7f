import { NgModule } from "@angular/core";
import { RouterModule, Routes } from "@angular/router";
import { IntegracaoAcessoFormComponent } from "./components/integracao-acesso-form/integracao-acesso-form.component";
import { IntegracaoAcessoComponent } from "./components/integracao-acesso/integracao-acesso.component";
import { ServidorFacialFormComponent } from "./components/servidor-facial-form/servidor-facial-form.component";
import { ServidorFacialComponent } from "./components/servidor-facial/servidor-facial.component";

const routes: Routes = [
	{
		path: "integracao-acesso",
		component: IntegracaoAcessoComponent,
	},
	{
		path: "nova-integracao-acesso",
		component: IntegracaoAcessoFormComponent,
	},
	{
		path: "integracao-acesso/:id",
		component: IntegracaoAcessoFormComponent,
	},
	{
		path: "servidor-facial",
		component: ServidorFacialComponent,
	},
	{
		path: "novo-servidor-facial",
		component: ServidorFacialFormComponent,
	},
	{
		path: "servidor-facial/:id",
		component: ServidorFacialFormComponent,
	},
];

@NgModule({
	imports: [RouterModule.forChild(routes)],
	exports: [RouterModule],
})
export class AcessoSitemaRoutingModule {}
