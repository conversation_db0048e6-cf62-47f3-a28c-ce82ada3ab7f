.container {
	padding: 16px;
	font-family: Poppins !important;

	::ng-deep.table-content {
		padding: 0 !important;
	}
}

.div-superior {
	display: grid;
	grid-gap: 16px;
}

.gap-1 {
	gap: 1rem;
}

.msg-alerta {
	color: #701028;
}

:host {
	::ng-deep {
		pacto-cat-form-textarea {
			.textarea-label {
				display: none;
			}
		}
	}
}

.btn-acoes {
	cursor: pointer;
	font-weight: 600;
	color: #1e60fa;
	font-family: Poppins !important;
	text-transform: capitalize;
}

.div-empresa {
	display: flex;
	align-items: center;
	padding-top: 30px;
}

.nome-empresa {
	padding-top: 30px;
}

.div-pessoa {
	display: flex;
	align-items: center;
	gap: 5px;
}

.div-acoes {
	display: grid;
	gap: 5px;
}
