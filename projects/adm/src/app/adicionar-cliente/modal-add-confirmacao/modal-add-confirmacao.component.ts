import { Component, EventEmitter, OnInit, Output } from "@angular/core";
import { NgbActiveModal } from "@ng-bootstrap/ng-bootstrap";

@Component({
	selector: "adm-modal-add-confirmacao",
	templateUrl: "./modal-add-confirmacao.component.html",
	styleUrls: ["./modal-add-confirmacao.component.scss"],
})
export class ModalAddConfirmacaoComponent implements OnInit {
	dados: any;
	@Output() update = new EventEmitter<any>();

	constructor(private modal: NgbActiveModal) {}

	ngOnInit() {}

	sim(): void {
		this.update.emit("SIM");
		this.modal.dismiss();
	}

	nao(): void {
		this.update.emit("NAO");
		this.modal.dismiss();
	}
}
