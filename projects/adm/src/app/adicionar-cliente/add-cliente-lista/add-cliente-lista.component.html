<div class="table-container">
	<table>
		<thead>
			<tr>
				<th *ngFor="let column of colluns" (click)="sortTable(column)">
					<span class="pct-title5 d-flex align-items-center">
						<!-- Exibir o label corretamente -->
						{{ column.label }}
						<i
							*ngIf="sortedColumn === column.key"
							[ngClass]="getSortIcon(column)"
							class="pct text-color-action-default-able-4"></i>
						<i
							*ngIf="sortedColumn !== column.key"
							class="pct pct-drop-down text-color-action-default-able-4"></i>
					</span>
				</th>
			</tr>
		</thead>

		<tbody>
			<tr *ngFor="let cliente of dataClientes">
				<td (click)="selecionarCliente(cliente)">
					<ds3-avatar [src]="cliente.urlFoto" size="32"></ds3-avatar>
					<span>{{ cliente.nome }}</span>
				</td>
				<td (click)="selecionarCliente(cliente)">{{ cliente.cpf }}</td>
				<td (click)="selecionarCliente(cliente)">
					{{ cliente.telefones && cliente.telefones[0].numero }}
				</td>
				<td (click)="selecionarCliente(cliente)">{{ cliente.origem }}</td>
				<td (click)="selecionarCliente(cliente)">
					{{ cliente.empresa.nomeCurto || cliente.empresa.nome }}
				</td>
				<td (click)="selecionarCliente(cliente)">
					{{ cliente.emails && cliente.emails[0].email }}
				</td>
				<td (click)="selecionarCliente(cliente)">
					<span [ngClass]="getSituacaoClass(cliente.situacao)">
						{{ cliente.situacao }}
					</span>
				</td>
			</tr>
		</tbody>
	</table>
</div>
<div class="d-flex flex-row-reverse mt-3">
	<button class="col-2" (click)="adicionar()" ds3-flat-button>
		Adicionar novo
		<i class="pct pct-plus ml-2"></i>
	</button>
</div>
