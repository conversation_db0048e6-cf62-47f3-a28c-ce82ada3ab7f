@import "projects/ui/assets/ds3/fonts/fonts.scss";
@import "projects/ui/assets/ds3/colors.var.scss";

.table-container {
	border: 1px solid #c9cbcf;
	border-radius: 8px;
	margin-top: 16px;
}

table {
	width: 100%;
	padding: 8px;
	border-collapse: separate;
	border-spacing: 0;
}

thead {
	min-height: 56px;
	box-shadow: inset 0 -1px 0 #c9cbcf;
}

th,
td {
	padding: 16px;
	text-align: left;
	font-family: Nunito Sans;
	font-weight: 400;
	font-size: 12px;
	line-height: 16px;
	letter-spacing: 0px;
	color: #494b50;
	cursor: pointer;
}

th {
	cursor: pointer;
	font-weight: bold;
	position: relative;
	color: $typeDefaultTitle;
	text-align: center;
}

th span {
	display: flex;
	align-items: center;
	gap: 5px;
}

th .sort-icon {
	font-size: 12px;
	color: #007bff;
	margin-left: 5px;
}

th::after {
	content: "";
	font-size: 12px;
	color: #007bff;
}

.profile-img {
	width: 32px;
	height: 32px;
	border-radius: 50%;
	margin-right: 8px;
	vertical-align: middle;
}

td a {
	color: #1e60fa;
	font-weight: bold;
	text-decoration: none;
	font-family: Poppins;
	font-weight: 600;
	font-size: 12px;
	line-height: 12px;
	letter-spacing: 0.25px;
}

td span {
	padding: 5px 16px;
	border-radius: 12px;
	font-family: Nunito Sans;
	font-weight: 400;
	font-size: 12px;
	line-height: 16px;
	letter-spacing: 0px;
	text-align: center;
}

.status-vi {
	background: #bccdf5;
	color: #163e9c;
}

.status-de {
	background: #ef8fa7;
	color: #701028;
}
