import { Component, EventEmitter, Input, OnInit, Output } from "@angular/core";
import { LayoutNavigationService, PlataformModuleConfig } from "pacto-layout";
import { ClientDiscoveryService, SessionService } from "sdk";
import { Router } from "@angular/router";
import { AdicionarClienteService } from "@adm/adicionar-cliente/adicionar-cliente.service";

@Component({
	selector: "adm-add-cliente-lista",
	templateUrl: "./add-cliente-lista.component.html",
	styleUrls: ["./add-cliente-lista.component.scss"],
})
export class AddClienteListaComponent implements OnInit {
	@Input() dataClientes: any[] = [];
	@Output() callchangeMode = new EventEmitter<string>();

	sortedColumn: string = "";
	sortOrder: "asc" | "desc" | "" = "";
	colluns = [
		{ label: "Nome", key: "nome" },
		{ label: "CPF", key: "cpf" },
		{ label: "Telefone", key: "telefone" },
		{ label: "Origem", key: "origem" },
		{ label: "Empresa", key: "empresa" },
		{ label: "E-mail", key: "email" },
		{ label: "Situação", key: "situacao" },
	];

	constructor(
		private navigationService: LayoutNavigationService,
		public sessionService: SessionService,
		private router: Router,
		private clientDiscoveryService: ClientDiscoveryService,
		private readonly adicionarClienteService: AdicionarClienteService
	) {}

	ngOnInit() {}

	sortTable(column: any): void {
		const key = column.key;
		if (this.sortedColumn === key) {
			this.sortOrder = this.sortOrder === "asc" ? "desc" : "asc";
		} else {
			this.sortedColumn = key;
			this.sortOrder = "asc";
		}

		this.dataClientes.sort((a, b) => {
			const valA = a[key] || "";
			const valB = b[key] || "";

			let comparison = 0;

			if (typeof valA === "string" && typeof valB === "string") {
				comparison = valA.localeCompare(valB);
			} else {
				comparison = valA < valB ? -1 : valA > valB ? 1 : 0;
			}

			if (comparison === 0) {
				comparison = a["nome"].localeCompare(b["nome"]) || 0;
			}

			return this.sortOrder === "asc" ? comparison : -comparison;
		});
	}

	getSortIcon(column: any): string {
		if (this.sortedColumn === column.key) {
			return this.sortOrder === "asc" ? "pct-caret-up" : "pct-caret-down";
		}
		return "pct-drop-down";
	}

	getSituacaoClass(situacao: string): string {
		return situacao === "VI" ? "status-vi" : "status-de";
	}

	adicionar() {
		this.callchangeMode.emit("add");
		this.adicionarClienteService.clienteSelecionado = null;
		this.router.navigateByUrl("/adm/adicionar-cliente/cadastro");
	}

	selecionarCliente(cliente) {
		if (cliente.matricula && cliente.matricula.length > 0) {
			this.router.navigateByUrl("/pessoas/perfil-v2/" + cliente.matricula);
		} else {
			this.adicionarClienteService.clienteSelecionado = cliente;
			this.router.navigateByUrl("/adm/adicionar-cliente/cadastro");
		}
	}
}
