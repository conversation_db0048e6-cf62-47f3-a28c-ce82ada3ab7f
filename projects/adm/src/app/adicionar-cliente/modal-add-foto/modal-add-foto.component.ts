import {
	ChangeDetectorRef,
	Component,
	EventEmitter,
	Inject,
	OnInit,
	Output,
} from "@angular/core";
import { FormControl, FormGroup } from "@angular/forms";
import { MAT_DIALOG_DATA, MatDialogRef } from "@angular/material/dialog";
import { AdmLegadoTelaClienteService } from "adm-legado-api";
import { SnotifyService } from "ng-snotify";
import { SessionService } from "@base-core/client/session.service";
import { AdicionarClienteService } from "@adm/adicionar-cliente/adicionar-cliente.service";

@Component({
	selector: "adm-modal-add-foto",
	templateUrl: "./modal-add-foto.component.html",
	styleUrls: ["./modal-add-foto.component.scss"],
})
export class ModalAddFotoComponent implements OnInit {
	@Output() public update: EventEmitter<void> = new EventEmitter<void>();

	public selecionado: "webcam" | "uploadDoComputador" = "webcam";
	public etapa: "vizualizacao" | "edicao" | "finalizacao" = "vizualizacao";
	public enableCapture: boolean = true;

	public formGroup = new FormGroup({
		fotoAluno: new FormControl(""),
		imageName: new FormControl(""),
	});

	public imagem: string = "";

	constructor(
		@Inject(MAT_DIALOG_DATA) public data: any,
		public dialogRef: MatDialogRef<ModalAddFotoComponent>,
		private readonly cd: ChangeDetectorRef,
		private readonly sessionService: SessionService,
		private readonly snotifyService: SnotifyService,
		private readonly adicionarClienteService: AdicionarClienteService
	) {}

	public ngOnInit(): void {
		if (this.adicionarClienteService.clienteSelecionado.urlFoto) {
			this.etapa = "vizualizacao";
			this.imagem = this.adicionarClienteService.clienteSelecionado.urlFoto;
			this.cd.detectChanges();
		} else {
			this.etapa = "vizualizacao";
			this.imagem = "";
			this.cd.detectChanges();
		}

		this.formGroup.get("fotoAluno").valueChanges.subscribe((value: string) => {
			if (value) {
				this.etapa = "finalizacao";
				this.imagem = value;
				this.cd.detectChanges();
			}
		});
	}

	public capturar(event: string): void {
		this.enableCapture = false;
		this.formGroup.get("fotoAluno").setValue(event);
	}

	public cancelar(): void {
		this.etapa = "vizualizacao";
		this.formGroup.reset();
		this.enableCapture = true;
		this.imagem = this.adicionarClienteService.clienteSelecionado.urlFoto;
	}

	public removerImagem(): void {
		this.snotifyService.success("Imagem excluída com sucesso");
		this.adicionarClienteService.clienteSelecionado.foto = "";
		this.adicionarClienteService.clienteSelecionado.urlFoto = "";
		this.closeModal(true);
		this.update.emit();
	}

	public editarImagem(): void {
		this.etapa = "edicao";
	}

	public tentarNovamente(): void {
		this.formGroup.reset();
		this.enableCapture = true;
		this.imagem = null;
		this.etapa = "edicao";
	}

	public salvarImagem(): void {
		const imageFotoAluno64 = this.imagem.replace(
			/^data:image\/(png|jpg|jpeg);base64,/,
			""
		);

		let body = {
			imagem: imageFotoAluno64,
		};

		this.adicionarClienteService.fotoTemporaria(body).subscribe(
			(res) => {
				if (res.content) {
					this.snotifyService.success("Imagem salva com sucesso");
					this.adicionarClienteService.clienteSelecionado.foto =
						imageFotoAluno64;
					this.adicionarClienteService.clienteSelecionado.urlFoto = res.content;
					this.closeModal(true);
					this.update.emit();
				} else {
					this.snotifyService.error(res.meta.message);
				}
			},
			({ error }) => {
				this.snotifyService.error(error.meta.message);
			}
		);
	}

	closeModal(value: boolean) {
		this.dialogRef.close(value);
	}
}
