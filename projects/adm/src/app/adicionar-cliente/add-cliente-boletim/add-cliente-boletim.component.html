<adm-layout
	i18n-pageTitle="@@adicionar-cliente-questionario:title"
	pageTitle="{{ pageTitle | captalize }}"
	i18n-modulo="@@adicionar-cliente-questionario:modulo"
	modulo="Administrativo"
	[formVersaoBeta]="'https://pacto.in/feedback-adicao-de-novo-cliente'"
	(goBack)="voltar()"
	appStickyFooter>
	<pacto-cat-card-plain>
		<section>
			<div>
				<form [formGroup]="form">
					<div class="row">
						<div class="col-5">
							<div class="img">
								<div
									class="contentAvatar"
									id="add-cliente-boletim-foto"
									(click)="changeAvatar()">
									<ds3-avatar [src]="urlFoto" size="64"></ds3-avatar>
									<span>{{ !!urlFoto ? "Trocar foto" : "Incluir foto" }}</span>
								</div>
							</div>
							<ds3-form-field>
								<ds3-field-label>Consultor *</ds3-field-label>
								<ds3-select
									id="add-cliente-boletim-consultor"
									placeholder="-"
									[nameKey]="'nome'"
									[options]="consultorList"
									[valueKey]="'codigo'"
									ds3Input
									formControlName="consultor"></ds3-select>
							</ds3-form-field>

							<ds3-form-field>
								<ds3-field-label>Evento</ds3-field-label>
								<ds3-select
									id="add-cliente-boletim-evento"
									placeholder="-"
									[nameKey]="'descricao'"
									[options]="eventoList"
									[valueKey]="'codigo'"
									ds3Input
									formControlName="evento"></ds3-select>
							</ds3-form-field>

							<ds3-form-field>
								<ds3-field-label>Data</ds3-field-label>
								<ds3-input-date
									required
									id="add-cliente-boletim-data"
									ds3Input
									[control]="form.get('data')"
									startView="month"
									dateType="datepicker"></ds3-input-date>
							</ds3-form-field>

							<ds3-form-field>
								<ds3-field-label>Freepass</ds3-field-label>
								<ds3-select
									id="add-cliente-boletim-freepass"
									placeholder="-"
									[nameKey]="'descricao'"
									[options]="freepassList"
									[valueKey]="'codigo'"
									[addEmptyOption]="true"
									ds3Input
									formControlName="freepass"></ds3-select>
							</ds3-form-field>

							<ds3-form-field>
								<ds3-field-label>Observação</ds3-field-label>
								<textarea
									id="add-cliente-boletim-observacao"
									ds3Input
									formControlName="observacao"
									rows="5"></textarea>
							</ds3-form-field>

							<ds3-form-field>
								<ds3-field-label>Tipo de vínculo</ds3-field-label>
								<ds3-select
									id="add-cliente-boletim-tipo-vinculo"
									placeholder="-"
									[nameKey]="'descricao'"
									[options]="tipoVinculoList"
									[valueKey]="'codigo'"
									(valueChanges)="obterColaboradorPorTipo()"
									ds3Input
									formControlName="tipoVinculo"></ds3-select>
							</ds3-form-field>

							<ds3-form-field>
								<ds3-field-label>Colaborador</ds3-field-label>
								<ds3-select
									id="add-cliente-boletim-colaborador"
									placeholder="-"
									[nameKey]="'descricao'"
									[valueKey]="'codigo'"
									[options]="colaboradorList"
									ds3Input
									formControlName="colaborador"></ds3-select>
							</ds3-form-field>

							<ds3-form-field>
								<div class="div-add-vinculo">
									<span
										class="add-vinculo cursorPointer"
										(click)="adicionarVinculo()">
										<i class="pct pct-plus-circle"></i>
										Adicionar vínculo
									</span>
								</div>
							</ds3-form-field>

							<ds3-form-field *ngIf="vinculosList.length > 0">
								<div class="div-vinculos">
									<div class="div-vinculos-item-header">
										<div class="div-vinculos-label-header">Tipo de vínculo</div>
										<div class="div-vinculos-label-header">Colaborador</div>
										<div class="div-vinculos-label-header"></div>
									</div>
									<div
										class="div-vinculos-item"
										*ngFor="
											let vinculo of vinculosList;
											let indexVinculo = index
										">
										<div class="div-vinculos-label">
											{{ tipoVinculoApresentar(vinculo?.tipoVinculo) }}
										</div>
										<div class="div-vinculos-label">
											{{ vinculo?.nomeColaborador }}
										</div>
										<div class="div-vinculos-label iconRemover cursorPointer">
											<i
												class="pct pct-x"
												(click)="removerVinculo(indexVinculo)"></i>
										</div>
									</div>
								</div>
							</ds3-form-field>

							<ds3-diviser class="mt-4" *ngIf="showGymPass"></ds3-diviser>
							<ds3-field-label *ngIf="showGymPass">Wellhub</ds3-field-label>

							<ds3-form-field *ngIf="showGymPass">
								<ds3-field-label>Tipo de acesso</ds3-field-label>
								<ds3-select
									id="add-cliente-gympass-tipo"
									placeholder="-"
									[nameKey]="'label'"
									[valueKey]="'id'"
									[options]="tiposTokenGympass"
									ds3Input
									formControlName="tipoAcessoGymPass"></ds3-select>
							</ds3-form-field>

							<ds3-form-field *ngIf="showGymPass">
								<ds3-field-label>Token Wellhub</ds3-field-label>
								<input
									ds3Input
									formControlName="gymidGymPass"
									id="add-cliente-gympass-gymid"
									type="text" />
							</ds3-form-field>

							<ds3-form-field *ngIf="showGymPass && showProdutoGymPass">
								<ds3-field-label>Produto Wellhub</ds3-field-label>
								<ds3-select
									id="add-cliente-gympass-produto"
									placeholder="-"
									[nameKey]="'descricao'"
									[valueKey]="'codigo'"
									[options]="wellhubList"
									ds3Input
									formControlName="produtoGymPass"></ds3-select>
							</ds3-form-field>
						</div>
						<div class="col-7 border">
							<div
								formGroupName="questionarioClienteGroup"
								class="modal-bv-questionario">
								<ng-container
									*ngFor="
										let questionarioPerguntaCliente of questionarioCliente?.questionarioPerguntaCliente;
										let indexPergunta = index
									">
									<ng-container
										class="div-pergunta-questionario"
										*ngIf="
											questionarioPerguntaCliente?.perguntaCliente
												?.tipoPergunta === 'NS'
										">
										<ds3-form-field
											[formGroupName]="groupNsPrefix + indexPergunta">
											<ds3-field-label class="nome-pergunta-questionario">
												{{
													questionarioPerguntaCliente?.perguntaCliente
														?.descricao
												}}
												<ng-container
													*ngIf="
														questionarioPerguntaCliente?.perguntaCliente
															?.obrigatoria || bvObrigatorio
													">
													*
												</ng-container>
											</ds3-field-label>
											<div class="d-flex">
												<div style="padding-right: 16px">
													<spa>Não indica</spa>
												</div>
												<ds3-checkbox
													[square]="true"
													ds3Input
													style="margin-right: 1rem"
													*ngFor="
														let resposta of questionarioPerguntaCliente
															?.perguntaCliente?.respostaPergCliente;
														let indexResposta = index
													"
													[formControlName]="controlNsPrefix + indexResposta">
													{{ resposta.descricaoRespota }}
												</ds3-checkbox>
												<div>
													<spa>Indica</spa>
												</div>
											</div>
										</ds3-form-field>
									</ng-container>
									<ng-container
										class="div-pergunta-questionario"
										*ngIf="
											questionarioPerguntaCliente?.perguntaCliente
												?.tipoPergunta === 'ME'
										">
										<ds3-form-field
											[formGroupName]="groupMePrefix + indexPergunta">
											<ds3-field-label class="nome-pergunta-questionario">
												{{
													questionarioPerguntaCliente?.perguntaCliente
														?.descricao
												}}
												<ng-container
													*ngIf="
														questionarioPerguntaCliente?.perguntaCliente
															?.obrigatoria || bvObrigatorio
													">
													*
												</ng-container>
											</ds3-field-label>
											<div class="modal-bv-options-grid">
												<ds3-checkbox
													[square]="true"
													ds3Input
													style="margin-right: 1rem"
													*ngFor="
														let resposta of questionarioPerguntaCliente
															?.perguntaCliente?.respostaPergCliente;
														let rI = index
													"
													[formControlName]="controlMePrefix + rI">
													{{ resposta.descricaoRespota }}
												</ds3-checkbox>
											</div>
										</ds3-form-field>
									</ng-container>
									<ng-container
										class="div-pergunta-questionario"
										*ngIf="
											questionarioPerguntaCliente?.perguntaCliente
												?.tipoPergunta === 'SE'
										">
										<ds3-form-field
											[formGroupName]="groupSePrefix + indexPergunta">
											<ds3-field-label class="nome-pergunta-questionario">
												{{
													questionarioPerguntaCliente?.perguntaCliente
														?.descricao
												}}
												<ng-container
													*ngIf="
														questionarioPerguntaCliente?.perguntaCliente
															?.obrigatoria || bvObrigatorio
													">
													*
												</ng-container>
											</ds3-field-label>
											<div class="modal-bv-options-grid">
												<ds3-checkbox
													[square]="true"
													ds3Input
													style="margin-right: 1rem"
													*ngFor="
														let resposta of questionarioPerguntaCliente
															?.perguntaCliente?.respostaPergCliente;
														let rI = index
													"
													[formControlName]="controlSePrefix + rI">
													{{ resposta.descricaoRespota }}
												</ds3-checkbox>
											</div>
										</ds3-form-field>
									</ng-container>
									<ng-container
										class="div-pergunta-questionario"
										*ngIf="
											questionarioPerguntaCliente?.perguntaCliente
												?.tipoPergunta === 'SN'
										">
										<ds3-form-field
											[formGroupName]="groupSnPrefix + indexPergunta">
											<ds3-field-label class="nome-pergunta-questionario">
												{{
													questionarioPerguntaCliente?.perguntaCliente
														?.descricao
												}}
												<ng-container
													*ngIf="
														questionarioPerguntaCliente?.perguntaCliente
															?.obrigatoria || bvObrigatorio
													">
													*
												</ng-container>
											</ds3-field-label>
											<div class="modal-bv-options-grid">
												<ds3-checkbox
													[square]="true"
													ds3Input
													style="margin-right: 1rem"
													*ngFor="
														let resposta of questionarioPerguntaCliente
															?.perguntaCliente?.respostaPergCliente;
														let rI = index
													"
													[formControlName]="controlSnPrefix + rI">
													{{ resposta.descricaoRespota }}
												</ds3-checkbox>
											</div>
										</ds3-form-field>
									</ng-container>
									<ng-container
										class="div-pergunta-questionario"
										*ngIf="
											questionarioPerguntaCliente?.perguntaCliente
												?.tipoPergunta === 'TE'
										">
										<ds3-form-field
											[formGroupName]="groupTePrefix + indexPergunta">
											<ds3-field-label class="nome-pergunta-questionario">
												{{
													questionarioPerguntaCliente?.perguntaCliente
														?.descricao
												}}
												<ng-container
													*ngIf="
														questionarioPerguntaCliente?.perguntaCliente
															?.obrigatoria || bvObrigatorio
													">
													*
												</ng-container>
											</ds3-field-label>
											<textarea
												ds3Input
												rows="1"
												[formControlName]="controlTePrefix + '0'"></textarea>
										</ds3-form-field>
									</ng-container>
								</ng-container>
							</div>
						</div>
					</div>
				</form>
			</div>
			<div class="row">
				<div class="col-6">
					<div class="mt-4">
						<button
							id="add-cliente-voltar"
							ds3-outlined-button
							class="flex-container"
							(click)="voltar()">
							<i class="pct pct-chevron-left mr-2"></i>
							Voltar
						</button>
					</div>
				</div>
				<div class="col-6 pr-0">
					<div class="d-flex flex-row-reverse mt-4">
						<button
							id="add-cliente-realizar-negociacao"
							ds3-flat-button
							class="flex-container ml-2"
							(click)="validar(false)">
							Realizar negociação
						</button>
						<button
							id="add-cliente-salvar-visitante"
							ds3-outlined-button
							class="flex-container"
							(click)="validar(true)">
							Salvar visitante
						</button>
					</div>
				</div>
			</div>
		</section>
	</pacto-cat-card-plain>
</adm-layout>
