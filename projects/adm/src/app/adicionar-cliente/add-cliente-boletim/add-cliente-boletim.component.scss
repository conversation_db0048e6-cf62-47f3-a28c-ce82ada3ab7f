.border {
	border: 1px solid #c9cbcf;
	border-radius: 8px;
}

.img {
	display: flex;
	flex-direction: column;
	align-items: center;

	.contentAvatar {
		display: contents;
		span {
			color: #1e60fa;
			font-weight: bold;
			text-decoration: none;
			font-family: Poppins;
			font-weight: 600;
			font-size: 12px;
			line-height: 12px;
			letter-spacing: 0.25px;
			margin-top: 8px;
		}

		&:hover {
			cursor: pointer;

			.edit-overlay {
				opacity: 0.6;
			}
		}
	}
}

.modal-bv-questionario {
	padding-bottom: 15px;

	ds3-form-field {
		display: block;
		margin-bottom: 20px;
	}

	.modal-bv-options-grid {
		padding-left: 16px;
		padding-right: 16px;
		display: grid;
		grid-template-columns: repeat(3, 1fr);
		grid-row-gap: 20px;
	}
}

.nome-pergunta-questionario {
	padding-top: 15px;
}

.div-vinculos {
}

.div-vinculos-item-header {
	display: grid;
	grid-template-columns: 3fr 3fr 1fr;
	padding: 5px 0 5px 0;
	border-bottom-width: 0.5px;
	border-bottom-style: solid;
	border-color: #c9cbcf;
}

.div-vinculos-item {
	display: grid;
	grid-template-columns: 3fr 3fr 1fr;
	padding: 5px 0 5px 0;
	border-bottom-width: 0.5px;
	border-bottom-style: solid;
	border-color: #c9cbcf;
}

.cursorPointer {
	cursor: pointer;
}

.add-vinculo {
	font-family: Nunito Sans;
	font-size: 12px;
	font-weight: 700;
	color: #1e60fa;
}

.div-vinculos-label {
	font-family: Nunito Sans;
	font-size: 12px;
	font-weight: 700;
	color: #40424c;
}

.div-vinculos-label-header {
	font-family: Nunito Sans;
	font-size: 14px;
	font-weight: 700;
	color: #40424c;
}

.iconRemover {
	color: #fa1e1e;
	cursor: pointer;
	text-align: end;
	padding-right: 5px;
}

.div-add-vinculo {
	padding: 5px 0 8px 0;
}
