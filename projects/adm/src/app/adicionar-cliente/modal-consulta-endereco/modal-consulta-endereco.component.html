<div class="modal-consulta-endereco">
	<form [formGroup]="form">
		<ds3-form-field>
			<ds3-field-label>Estado</ds3-field-label>
			<ds3-select
				id="select-estado"
				placeholder="-"
				[options]="estadoList"
				[nameKey]="'descricao'"
				[valueKey]="'codigo'"
				ds3Input
				formControlName="estado"></ds3-select>
		</ds3-form-field>

		<ds3-form-field>
			<ds3-field-label>Cidade</ds3-field-label>
			<input
				id="input-cidade"
				ds3Input
				formControlName="cidade" />
		</ds3-form-field>

		<ds3-form-field>
			<ds3-field-label>Bairro</ds3-field-label>
			<input
				id="input-bairro"
				ds3Input
				formControlName="bairro" />
		</ds3-form-field>

		<ds3-form-field>
			<ds3-field-label>Endereço</ds3-field-label>
			<input
				id="input-logradouro"
				ds3Input
				formControlName="logradouro" />
		</ds3-form-field>
	</form>
	<div class="msg-info">
		<span>Informe o nome ou parte do seu endereço, rua ou avenida. Não inclua o tipo da via nem o número da casa.</span>
	</div>
	<div class="div-btn">
		<button ds3-flat-button id="btn-consultar-action"
						(click)="consultar()">
			<span>Consultar</span>
		</button>
	</div>
	<div class="div-table">
		<pacto-relatorio
			#tableData
			id="rel-consulta-endereco"
			[persistirFiltros]="false"
			[enableZebraStyle]="true"
			[baseFilter]="baseFilter"
			[showShare]="false"
			[table]="table"
			[customEmptyContent]="emptyState"
			[enableDs3]="true"
			(rowClick)="selecionar($event)"></pacto-relatorio>
	</div>
</div>

<ng-template #emptyState>
</ng-template>
