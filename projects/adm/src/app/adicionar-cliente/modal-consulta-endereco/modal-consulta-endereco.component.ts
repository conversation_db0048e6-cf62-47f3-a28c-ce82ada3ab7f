import { AfterViewInit, ChangeDetectorRef, Component, EventEmitter, OnInit, Output, ViewChild } from "@angular/core";
import { FormControl, FormGroup } from "@angular/forms";
import { NgbActiveModal } from "@ng-bootstrap/ng-bootstrap";
import { SessionService } from "@base-core/client/session.service";
import { SnotifyService } from "ng-snotify";
import { CaptalizePipe, DataFiltro, PactoDataGridConfig, RelatorioComponent } from "ui-kit";
import { AdicionarClienteService } from "@adm/adicionar-cliente/adicionar-cliente.service";

@Component({
	selector: "adm-modal-consulta-endereco",
	templateUrl: "./modal-consulta-endereco.component.html",
	styleUrls: ["./modal-consulta-endereco.component.scss"],
})
export class ModalConsultaEnderecoComponent implements OnInit, AfterViewInit {

	form: FormGroup;
	@Output() update = new EventEmitter<any>();
	table: PactoDataGridConfig;
	@ViewChild("tableData", { static: false }) tableData: RelatorioComponent;
	baseFilter: DataFiltro = {};
	consultou = false;
	estadoList = [
		{ codigo: "", descricao: "-" },
		{ codigo: "AC", descricao: "Acre" },
		{ codigo: "AL", descricao: "Alagoas" },
		{ codigo: "AP", descricao: "Amapá" },
		{ codigo: "AM", descricao: "Amazonas" },
		{ codigo: "BA", descricao: "Bahia" },
		{ codigo: "CE", descricao: "Ceará" },
		{ codigo: "DF", descricao: "Distrito Federal" },
		{ codigo: "ES", descricao: "Espírito Santo" },
		{ codigo: "GO", descricao: "Goiás" },
		{ codigo: "MA", descricao: "Maranhão" },
		{ codigo: "MT", descricao: "Mato Grosso" },
		{ codigo: "MS", descricao: "Mato Grosso do Sul" },
		{ codigo: "MG", descricao: "Minas Gerais" },
		{ codigo: "PA", descricao: "Pará" },
		{ codigo: "PB", descricao: "Paraíba" },
		{ codigo: "PR", descricao: "Paraná" },
		{ codigo: "PE", descricao: "Pernambuco" },
		{ codigo: "PI", descricao: "Piauí" },
		{ codigo: "RJ", descricao: "Rio de Janeiro" },
		{ codigo: "RN", descricao: "Rio Grande do Norte" },
		{ codigo: "RS", descricao: "Rio Grande do Sul" },
		{ codigo: "RO", descricao: "Rondônia" },
		{ codigo: "RR", descricao: "Roraima" },
		{ codigo: "SC", descricao: "Santa Catarina" },
		{ codigo: "SP", descricao: "São Paulo" },
		{ codigo: "SE", descricao: "Sergipe" },
		{ codigo: "TO", descricao: "Tocantins" },
	];
	lista: any[] = [];

	constructor(
		private modal: NgbActiveModal,
		private sessionService: SessionService,
		private snotifyService: SnotifyService,
		private cd: ChangeDetectorRef,
		private readonly adicionarClienteService: AdicionarClienteService,
	) {
	}

	ngOnInit() {
		this.initForm();
		this.initTable();
		if (this.sessionService.currentEmpresa &&
			this.sessionService.currentEmpresa.siglaEstado) {
			this.form.get("estado").setValue(this.sessionService.currentEmpresa.siglaEstado);
		}
		this.cd.detectChanges();
	}

	ngAfterViewInit() {
		this.cd.detectChanges();
	}

	initForm() {
		this.form = new FormGroup({
			estado: new FormControl(null),
			cidade: new FormControl(null),
			bairro: new FormControl(null),
			logradouro: new FormControl(null),
		});
	}

	initTable() {
		setTimeout(() => {
			this.table = new PactoDataGridConfig({
				dataAdapterFn: (serverData) => {
					return {
						content: this.lista,
						first: true,
						last: false,
						number: 0,
						size: this.lista.length,
						totalElements: this.lista.length,
					};
				},
				quickSearch: false,
				ghostLoad: true,
				pagination: false,
				ghostAmount: 5,
				showFilters: false,
				columns: [
					{
						nome: "enderecoCep",
						titulo: "CEP",
						visible: true,
						ordenavel: false,
					},
					{
						nome: "cidadeDescricao",
						titulo: "Cidade",
						visible: true,
						ordenavel: false,
					},
					{
						nome: "bairroDescricao",
						titulo: "Bairro",
						visible: true,
						ordenavel: false,
						styleClass: "valorCampo-column",
					},
					{
						nome: "enderecoLogradouro",
						titulo: "Endereço",
						visible: true,
						ordenavel: false,
						styleClass: "valorCampo-column",
					},
				],
			});
			this.cd.detectChanges();
		});
	}

	consultar() {
		const filtros = {
			ufSigla: this.form.get("estado").value,
			cidadeDescricao: this.form.get("cidade").value,
			bairroDescricao: this.form.get("bairro").value,
			enderecoLogradouro: this.form.get("logradouro").value
		};
		const params = {
			page: 0,
			size: 10,
		};
		this.adicionarClienteService
			.consultarEndereco(filtros, params)
			.subscribe(
				(resp) => {
					this.consultou = true;
					this.lista = resp.content || [];
					if (this.lista.length === 0) {
						this.snotifyService.info("Nenhum endereço encontrado");
					}
					this.tableData.reloadData();
					this.cd.detectChanges();
				},
				({ error = {} }) => {
					const message =
						error.meta && error.meta.message
							? error.meta.message
							: "Erro ao consultar endereço";
					this.snotifyService.error(message);
					this.cd.detectChanges();
				},
			);
	}

	selecionar(row) {
		this.update.emit(row);
		this.modal.close();
	}

	close(): void {
		this.modal.dismiss();
	}
}
