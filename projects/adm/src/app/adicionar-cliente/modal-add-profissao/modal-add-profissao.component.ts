import {
	ChangeDetectorRef,
	Component,
	EventEmitter,
	OnInit,
	Output,
} from "@angular/core";
import { FormControl, FormGroup } from "@angular/forms";
import { NgbActiveModal } from "@ng-bootstrap/ng-bootstrap";
import { SessionService } from "@base-core/client/session.service";
import { SnotifyService } from "ng-snotify";
import { CadastroAuxApiProfissaoService } from "cadastro-aux-api";
import { Profissao } from "@adm/cadastro-auxliar/classes/profissao";

@Component({
	selector: "adm-modal-add-profissao",
	templateUrl: "./modal-add-profissao.component.html",
	styleUrls: ["./modal-add-profissao.component.scss"],
})
export class ModalAddProfissaoComponent implements OnInit {
	form: FormGroup;
	profissao: Profissao = new Profissao();
	@Output() update = new EventEmitter<void>();

	constructor(
		private modal: NgbActiveModal,
		private profissaoService: CadastroAuxApiProfissaoService,
		private sessionService: SessionService,
		private snotifyService: SnotifyService,
		private cd: ChangeDetectorRef
	) {}

	ngOnInit() {
		this.form = new FormGroup({
			descricao: new FormControl(),
		});
	}

	salvar(): void {
		Object.keys(this.form.getRawValue()).forEach((key) => {
			this.profissao[key] = this.form.getRawValue()[key];
		});
		this.profissaoService.save(this.profissao).subscribe(
			(response) => {
				this.snotifyService.success("Profissão cadastrada com sucesso!");
				this.update.emit(response);
				this.close();
			},
			(httpErrorResponse) => {
				const err = httpErrorResponse.error;
				if (err.meta && err.meta.messageValue) {
					this.snotifyService.error(err.meta.messageValue);
				}
			}
		);
	}

	close(): void {
		this.modal.dismiss();
	}
}
