import { NgModule } from "@angular/core";
import { Routes, RouterModule } from "@angular/router";
import { VendaAvulsaComponent } from "@adm/venda-avulsa/venda-avulsa.component";
import { AddClienteBoletimComponent } from "@adm/adicionar-cliente/add-cliente-boletim/add-cliente-boletim.component";
import { AddClienteCadastroComponent } from "@adm/adicionar-cliente/add-cliente-cadastro/add-cliente-cadastro.component";
import { AddClienteListaComponent } from "@adm/adicionar-cliente/add-cliente-lista/add-cliente-lista.component";
import { AdicionarClienteComponent } from "@adm/adicionar-cliente/adicionar-cliente.component";

const routes: Routes = [
	{
		path: "",
		component: AdicionarClienteComponent,
	},
	{
		path: "cadastro",
		component: AddClienteCadastroComponent,
	},
	{
		path: "boletim",
		component: AddClienteBoletimComponent,
	},
];

@NgModule({
	imports: [RouterModule.forChild(routes)],
	exports: [RouterModule],
})
export class AdicionarClienteRoutingModule {}
