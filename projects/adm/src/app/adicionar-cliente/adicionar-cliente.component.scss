@import "projects/ui/assets/ds3/fonts/fonts.scss";
@import "projects/ui/assets/ds3/colors.var.scss";

.cabecalho-principal {
	display: flex;
	flex-direction: column;

	.seta {
		cursor: pointer;
		color: #51555a;
		font-size: 1.5rem;
		font-weight: 400;
		margin-right: 12px;
		width: 25px;
	}

	.breadcrumbs {
		display: flex;
		grid-column: 2 / 3;
		grid-row: 1 / 2;
		align-items: center;
		margin-bottom: 10px;
		margin-left: 36px;

		.mod,
		.sessao {
			font-family: Poppins !important;
			color: #797d86;
			font-size: 12px;
			font-weight: 600;
			line-height: 12px;
		}

		i {
			margin: 0 12px;
		}
	}

	.navegate {
		display: flex;
		margin-bottom: 24px;
	}

	.titulo {
		grid-column: 2 / 3;
		grid-row: 2 / 3;
		font-family: Poppins;
		font-size: 22px;
		font-weight: 600;
		line-height: 27.5px;
		letter-spacing: 0.25px;
		color: #55585e;
	}
}

.text-info {
	font-family: Poppins;
	font-size: 14px;
	font-weight: 400;
	line-height: 17.5px;
	text-align: left;
	text-underline-position: from-font;
	text-decoration-skip-ink: none;
	color: #797d86 !important;
}

.div-restricao {
  width: 100%;
  margin: 12px 0;
  padding: 6px 12px;
  color: #e10505;
  background: #fee6e6;
  border: 1px solid #fee6e6;
  box-sizing: border-box;
  border-radius: 8px;
  font-weight: 400;
  line-height: 17.5px;
  font-family: Poppins;
  text-align: justify;
  display: flex;
  align-items: center;
}

.text-restricao {
  font-family: Poppins;
  font-size: 14px;
  font-weight: 400;
  line-height: 17.5px;
  text-align: left;
  text-underline-position: from-font;
  text-decoration-skip-ink: none;
  color: #E10505 !important;
}

.bgFAFAFA {
	background-color: #fafafa;
	padding: 8px 16px;
}

.flex-container {
	height: 40px;
	display: flex;
	flex-direction: row;
	margin-top: auto;
	width: auto;
}

::ng-deep pacto-cat-card-plain {
	.ds3-form-field {
		.ds3-field-label {
			label {
				padding: 16px 0 0 !important;
			}
		}
	}
}

.table-container {
	border: 1px solid #c9cbcf;
	border-radius: 8px;
	margin-top: 16px;
}

table {
	width: 100%;
	padding: 8px;
	border-collapse: separate;
	border-spacing: 0;

	tbody tr:nth-child(even) {
		background-color: #f4f4f4;
	}
}

thead {
	min-height: 56px;
	box-shadow: inset 0 -1px 0 #c9cbcf;
}

th,
td {
	padding: 16px;
	text-align: left;
	font-family: Poppins !important;
	font-weight: 400;
	font-size: 12px;
	line-height: 16px;
	letter-spacing: 0px;
	color: #494b50;
	cursor: pointer;
}

th {
	cursor: pointer;
	font-weight: bold;
	position: relative;
	color: $typeDefaultTitle;
	text-align: center;
}

th span {
	display: flex;
	align-items: center;
	gap: 5px;
}

th .sort-icon {
	font-size: 12px;
	color: #007bff;
	margin-left: 5px;
}

th::after {
	content: "";
	font-size: 12px;
	color: #007bff;
}

.profile-img {
	width: 32px;
	height: 32px;
	border-radius: 50%;
	margin-right: 8px;
	vertical-align: middle;
}

td a {
	color: #1e60fa;
	font-weight: bold;
	text-decoration: none;
	font-family: Poppins;
	font-weight: 600;
	font-size: 12px;
	line-height: 12px;
	letter-spacing: 0.25px;
}

td span {
	padding: 5px 16px;
	border-radius: 12px;
	font-family: Poppins;
	font-weight: 400;
	font-size: 12px;
	line-height: 16px;
	letter-spacing: 0px;
	text-align: center;
}

.situacao-aluno {
	&.vi.primario {
		color: #163e9c;
		background-color: #bccdf5;
	}

	&.at.primario {
		color: #037d03;
		background-color: #b4fdb4;
	}

	&.in.primario {
		color: #7d0303;
		background-color: #fdb4b4;
	}

	&.tr.primario {
		color: #797d86;
		background-color: #e4e5e7;
	}

	&.no.secundario {
		color: #0a4326;
		background-color: #8fefbf;
	}

	&.di.secundario {
		color: #0a4326;
		background-color: #63e9a6;
	}

	&.pe.secundario {
		color: #0a4326;
		background-color: #1dc973;
	}

	&.av.secundario {
		color: #705810;
		background-color: #efd78f;
	}

	&.ve.secundario {
		color: #705810;
		background-color: #e9c763;
	}

	&.tv.secundario {
		color: #705810;
		background-color: #e2b736;
	}

	&.ca.secundario {
		color: #701028;
		background-color: #f5bcca;
	}

	&.de.secundario {
		color: #701028;
		background-color: #ef8fa7;
	}

	&.in.secundario {
		color: #701028;
		background-color: #e96384;
	}

	&.ae.secundario {
		color: #105870;
		background-color: #63c7e9;
	}

	&.cr.secundario {
		color: #105870;
		background-color: #8fd7ef;
	}

	&.gympass.terciario {
		color: #9c5316;
		background-color: #f5d6bc;
	}

	&.totalPass.terciario {
		color: #9c5316;
		background-color: #efba8f;
	}

	&.freepass.terciario {
		color: #0a4326;
		background-color: #1dc973;
	}

	&.dependente.terciario {
		color: #ffffff;
		background-color: #0a4326;
	}
}

.nome-cliente {
	color: #1e60fa;
	font-family: Poppins !important;
	text-transform: capitalize;
}

.linha {
	.flexed {
		justify-content: space-around;
		display: flex;
		flex-direction: row;
		padding: 0px;

		span.entre-campos {
			height: 84px;
			display: flex;
			padding: 10px;
			align-items: flex-end;
		}
	}

	button.col {
		margin-left: 20px;
		margin-right: 15px;
	}
}

.div-cliente-origem {
	display: flex;
	gap: 5px;
}
