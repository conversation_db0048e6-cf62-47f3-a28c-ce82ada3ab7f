<adm-layout
	i18n-pageTitle="@@adicionar-cliente:title"
	pageTitle="Adicionar cliente"
	i18n-modulo="@@negociacao:modulo"
	modulo="Administrativo"
	hideBackButton="true"
	[formVersaoBeta]="'https://pacto.in/feedback-adicao-de-novo-cliente'"
	(changeNovaVersao)="acessarVersaoAntiga($event)"
	[recurso]="recurso"
	appStickyFooter>
	<pacto-cat-card-plain>
		<section class="d-flex align-items-center bgFAFAFA mb-3">
			<i class="pct pct-info mr-2"></i>
			<span class="text-info">
				{{ textInfo }}
			</span>
		</section>
		<form [formGroup]="form">
			<div class="row">
				<div class="col d-flex">
					<ds3-form-field class="">
						<ds3-field-label>CPF</ds3-field-label>
						<input
							ds3Input
							formControlName="cpf"
							id="add-cliente-cpf"
							type="text"
							mask="00000000000" />
					</ds3-form-field>
					<span class="mt-5 ml-3">ou</span>
				</div>
				<div class="col d-flex pl-0">
					<ds3-form-field class="">
						<ds3-field-label>Nome</ds3-field-label>
						<input
							ds3Input
							formControlName="nome"
							id="add-cliente-nome"
							autofocus
							type="text" />
					</ds3-form-field>

					<span class="mt-5 ml-3">ou</span>
				</div>
				<div class="col d-flex pl-0">
					<ds3-form-field class="">
						<ds3-field-label>Telefone</ds3-field-label>
						<input
							ds3Input
							formControlName="telefone"
							id="add-cliente-telefone"
							mask="00000000000"
							type="text" />
					</ds3-form-field>

					<span class="mt-5 ml-3">ou</span>
				</div>
				<div class="col d-flex pl-0">
					<ds3-form-field class="">
						<ds3-field-label>E-mail</ds3-field-label>
						<input
							ds3Input
							formControlName="email"
							id="add-cliente-email"
							type="text" />
					</ds3-form-field>
				</div>
				<div class="col">
					<button
						class="w-100"
						id="add-cliente-btn-consultar"
						(click)="consultar()"
						ds3-outlined-button
						style="height: 40px; margin-top: 45px">
						Consultar
					</button>
				</div>
			</div>
		</form>
		<section *ngIf="textoRestricao" class="d-flex align-items-center div-restricao mb-3">
			<i class="pct pct-info mr-2"></i>
			<span class="text-restricao">
				{{ textoRestricao }}
			</span>
		</section>
		<ng-container *ngIf="consultou">
			<div class="table-container" *ngIf="clientes.length > 0">
				<table>
					<thead>
						<tr>
							<th *ngFor="let column of columns" (click)="sortTable(column)">
								<span class="pct-title5 d-flex align-items-center">
									<!-- Exibir o label corretamente -->
									{{ column.label }}
									<i
										*ngIf="sortedColumn === column.key"
										[ngClass]="getSortIcon(column)"
										id="add-cliente-sorted-up-{{ column.key }}"
										class="pct text-color-action-default-able-4"></i>
									<i
										*ngIf="sortedColumn !== column.key"
										id="add-cliente-sorted-down-{{ column.key }}"
										class="pct pct-drop-down text-color-action-default-able-4"></i>
								</span>
							</th>
						</tr>
					</thead>

					<tbody>
						<tr *ngFor="let cliente of clientes; let clienteIndex = index">
							<td
								id="add-cliente-cliente-nome-{{ clienteIndex }}"
								(click)="selecionarCliente(cliente)">
								<ds3-avatar [src]="cliente.urlFoto" size="32"></ds3-avatar>
								<span class="nome-cliente">
									{{ cliente.nome | captalize : true }}
								</span>
							</td>
							<td
								id="add-cliente-cliente-cpf-{{ clienteIndex }}"
								(click)="selecionarCliente(cliente)">
								{{ cliente.cpf }}
							</td>
							<td
								id="add-cliente-cliente-telefone-{{ clienteIndex }}"
								(click)="selecionarCliente(cliente)">
								{{ cliente.telefonesGeral }}
							</td>
							<td
								id="add-cliente-cliente-empresa-{{ clienteIndex }}"
								(click)="selecionarCliente(cliente)">
								{{
									cliente.empresa.nomeCurto || cliente.empresa.nome | captalize
								}}
							</td>
							<td
								id="add-cliente-cliente-email-{{ clienteIndex }}"
								(click)="selecionarCliente(cliente)">
								{{ cliente.emailsGeral }}
							</td>
							<td
								id="add-cliente-cliente-origem-{{ clienteIndex }}"
								(click)="selecionarCliente(cliente)">
								<div class="div-cliente-origem">
									<div>{{ cliente.origem | captalize }}</div>
									<div
										[ds3Tooltip]="
											(cliente.pessoaIndicou && cliente.pessoaIndicou.tipo
												? (cliente.pessoaIndicou.tipo | captalize)
												: 'Pessoa') + ' que indicou'
										"
										*ngIf="cliente.pessoaIndicou">
										{{
											cliente.pessoaIndicou
												? " | " + cliente.pessoaIndicou.nome
												: ("" | captalize)
										}}
									</div>
									<div
										[ds3Tooltip]="'Usuário responsável pelo cadastro'"
										*ngIf="!cliente.pessoaIndicou">
										{{
											cliente.usuarioCadastrou
												? " | " + cliente.usuarioCadastrou.nome
												: ("" | captalize)
										}}
									</div>
								</div>
							</td>
							<td
								id="add-cliente-cliente-situacao-{{ clienteIndex }}"
								(click)="selecionarCliente(cliente)">
								<span
									[class]="
										'situacao-aluno primario ' + cliente?.situacao | lowercase
									">
									{{ cliente.situacao }}
								</span>
							</td>
						</tr>
					</tbody>
				</table>
			</div>
			<div class="d-flex flex-row-reverse mt-3">
				<button
					class="col-2"
					id="add-cliente-adicionar-novo"
					(click)="adicionar()"
					ds3-flat-button>
					Adicionar novo
					<i class="pct pct-plus ml-3"></i>
				</button>
			</div>
		</ng-container>
	</pacto-cat-card-plain>
</adm-layout>
