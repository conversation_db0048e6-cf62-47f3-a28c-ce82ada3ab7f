import {
	ChangeDetectorRef,
	Component,
	EventEmitter,
	OnInit,
	Output,
	TemplateRef,
	ViewChild,
} from "@angular/core";
import { NgbActiveModal } from "@ng-bootstrap/ng-bootstrap";
import { SessionService } from "@base-core/client/session.service";
import { SnotifyService } from "ng-snotify";
import { FormGroup } from "@angular/forms";
import { CaptalizePipe, PactoDataGridConfig, RelatorioComponent } from "ui-kit";

@Component({
	selector: "adm-modal-cliente-semelhante",
	templateUrl: "./modal-cliente-semelhante.component.html",
	styleUrls: ["./modal-cliente-semelhante.component.scss"],
})
export class ModalClienteSemelhanteComponent implements OnInit {
	dados: any;
	clientes = [];
	convenios: [];
	apresentarIgnorar = false;
	form: FormGroup;
	@Output() update = new EventEmitter<any>();
	private captalizePipe = new CaptalizePipe();
	public table: PactoDataGridConfig;
	@ViewChild("colunaDependentes", { static: true })
	colunaDependentes: TemplateRef<any>;
	@ViewChild("colunaAcoes", { static: true }) colunaAcoes: TemplateRef<any>;
	@ViewChild("colunaPessoa", { static: true }) colunaPessoa: TemplateRef<any>;
	@ViewChild("colunaEmpresa", { static: true }) colunaEmpresa: TemplateRef<any>;
	@ViewChild("tableData", { static: true }) tableData: RelatorioComponent;
	itensPerPage = [
		{ id: 5, label: "5" },
		{ id: 10, label: "10" },
		{ id: 20, label: "20" },
	];

	constructor(
		private modal: NgbActiveModal,
		private sessionService: SessionService,
		private snotifyService: SnotifyService,
		private cd: ChangeDetectorRef
	) {}

	ngOnInit() {
		this.initTable();
	}

	initTable() {
		this.table = new PactoDataGridConfig({
			rowClick: false,
			pagination: true,
			dataAdapterFn: () => {
				return {
					size: 10,
					content: this.clientes,
					totalElements: this.clientes.length,
					number: 0,
				};
			},
			columns: [
				{
					nome: "pessoaDTOObj",
					titulo: "Pessoa",
					visible: true,
					ordenavel: false,
					celula: this.colunaPessoa,
				},
				{
					nome: "empresaDTO",
					titulo: "Empresa",
					visible: true,
					celula: this.colunaEmpresa,
					ordenavel: false,
				},
				{
					nome: "acoes",
					titulo: "Ações",
					celula: this.colunaAcoes,
					visible: true,
					ordenavel: false,
				},
			],
		});
	}

	close(): void {
		this.modal.dismiss();
	}

	transferir(cliente): void {
		const ret = {
			operacao: "TRANSFERIR",
			cliente,
		};
		this.update.emit(ret);
		this.close();
	}

	responsavel(cliente): void {
		const ret = {
			operacao: "RESPONSAVEL",
			cliente,
		};
		this.update.emit(ret);
		this.close();
	}

	editar(cliente): void {
		const ret = {
			operacao: "EDITAR",
			cliente,
		};
		this.update.emit(ret);
		this.close();
	}

	ignorar(): void {
		const ret = {
			operacao: "IGNORAR",
		};
		this.update.emit(ret);
		this.close();
	}

	informarOutro(): void {
		const ret = {
			operacao: "INFORMAR_OUTRO_CPF",
		};
		this.update.emit(ret);
		this.close();
	}

	empresaLogada() {
		return Number(this.sessionService.codigoEmpresa);
	}
}
