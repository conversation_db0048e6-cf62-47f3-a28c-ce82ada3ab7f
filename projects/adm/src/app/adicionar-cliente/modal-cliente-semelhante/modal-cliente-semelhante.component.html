<div class="container">
	<div class="div-superior">
		<pacto-relatorio
			#tableData
			[enableZebraStyle]="true"
			[itensPerPage]="itensPerPage"
			[showShare]="false"
			[table]="table"
			actionTitulo="Ações"
			id="modal-cliente-semelhante"
			i18n-actionTitulo="@@label-acoes"></pacto-relatorio>
	</div>
	<div class="d-flex justify-content-end gap-1">
		<pacto-cat-button
			*ngIf="apresentarIgnorar"
			(click)="ignorar()"
			id="btn-mdl-cliente-semelhante-ignorar"
			label="Ignorar"
			size="MEDIUM"
			type="OUTLINE"></pacto-cat-button>

		<pacto-cat-button
			(click)="informarOutro()"
			id="btn-mdl-cliente-semelhante-informar-outro"
			label="Informar outro CPF"
			size="MEDIUM"
			type="OUTLINE"></pacto-cat-button>
	</div>
</div>

<ng-template #colunaEmpresa let-item="item">
	<span class="nome-empresa">{{ item?.empresaDTO?.nome | captalize }}</span>
</ng-template>

<ng-template #colunaPessoa let-item="item">
	<div class="div-pessoa">
		<ds3-avatar [src]="item?.pessoaDTO?.urlFoto" size="45"></ds3-avatar>
		<div style="display: grid">
			<span>{{ item?.pessoaDTO?.nome | captalize }}</span>
			<span>Mat. {{ item?.matricula }}</span>
			<span *ngIf="item?.pessoaDTO?.cpf">CPF: {{ item?.pessoaDTO?.cpf }}</span>
			<span *ngIf="item?.pessoaDTO?.dataNasc">
				Nasc: {{ item?.pessoaDTO?.dataNasc | date : "dd/MM/yyyy" }}
			</span>
		</div>
	</div>
</ng-template>

<ng-template #colunaAcoes let-item="item" let-index="index">
	<div class="div-acoes">
		<span
			*ngIf="item?.empresaDTO?.codigo !== empresaLogada()"
			class="btn-acoes"
			id="btn-mdl-cliente-semelhante-transferir-{{ index }}"
			(click)="transferir(item)">
			Transferir de empresa
		</span>
		<span
			*ngIf="item?.empresaDTO?.codigo === empresaLogada()"
			class="btn-acoes"
			id="btn-mdl-cliente-semelhante-editar-{{ index }}"
			(click)="editar(item)">
			Editar
		</span>
		<span
			*ngIf="item?.empresaDTO?.codigo === empresaLogada()"
			class="btn-acoes"
			id="btn-mdl-cliente-semelhante-add-responsavel-{{ index }}"
			(click)="responsavel(item)">
			Adicionar responsável
		</span>
	</div>
</ng-template>

<ng-template
	#colunaDependentes
	let-data="data"
	let-index="index"
	let-item="item"></ng-template>
