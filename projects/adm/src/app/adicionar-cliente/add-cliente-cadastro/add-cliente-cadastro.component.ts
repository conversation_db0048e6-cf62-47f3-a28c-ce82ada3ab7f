import {
	AfterViewInit,
	ChangeDetectorRef,
	Component,
	ElementRef,
	EventEmitter,
	Input,
	OnInit,
	Output,
	ViewChild,
} from "@angular/core";
import {
	FormBuilder,
	FormControl,
	FormGroup,
	Validators,
} from "@angular/forms";
import { PactoApiCepService } from "pacto-api";
import { PerfilRecursoPermissoTipo } from "sdk";
import { SessionService } from "@base-core/client/session.service";
import { ToastrService } from "ngx-toastr";
import {
	AcessoCatracaService,
	AlunoColaboradorUsuarioService,
} from "pessoa-ms-api";
import {
	CampoLabelEnum,
	CampoMaskEnum,
	CampoMaxLengthEnum,
	CampoOrdem,
} from "@adm/adicionar-cliente/enum/cadastro-cliente.enum";
import { Router } from "@angular/router";
import { AdicionarClienteService } from "@adm/adicionar-cliente/adicionar-cliente.service";
import { catchError, pairwise } from "rxjs/operators";
import { of } from "rxjs";
import { RestService } from "@base-core/rest/rest.service";
import { PactoModalSize, SelectFilterParamBuilder } from "ui-kit";
import { ModalService } from "@base-core/modal/modal.service";
import { ModalAddProfissaoComponent } from "@adm/adicionar-cliente/modal-add-profissao/modal-add-profissao.component";
import { SnotifyService } from "ng-snotify";
import moment from "moment";
import { ModalTransferirClienteEmpresaComponent } from "@adm/adicionar-cliente/modal-transferir-cliente-empresa/modal-transferir-cliente-empresa.component";
import { ModalClienteSemelhanteComponent } from "@adm/adicionar-cliente/modal-cliente-semelhante/modal-cliente-semelhante.component";
import {
	ModalConsultaEnderecoComponent
} from "@adm/adicionar-cliente/modal-consulta-endereco/modal-consulta-endereco.component";

@Component({
	selector: "adm-add-cliente-cadastro",
	templateUrl: "./add-cliente-cadastro.component.html",
	styleUrls: ["./add-cliente-cadastro.component.scss"],
})
export class AddClienteCadastroComponent implements OnInit {
	form: FormGroup;
	sexoList = [
		{ id: "M", nome: "Masculino" },
		{ id: "F", nome: "Feminino" },
	];
	generoListANT = [
		{ id: "AG", nome: "Agênero" },
		{ id: "FC", nome: "Feminino cisgênero" },
		{ id: "MC", nome: "Masculino cisgênero" },
		{ id: "FT", nome: "Feminino transgênero" },
		{ id: "MT", nome: "Masculino transgênero" },
		{ id: "NB", nome: "Não-binário" },
	];
	generoList = [
		{ id: "AG", nome: "Agênero" },
		{ id: "FE", nome: "Feminino" },
		{ id: "MA", nome: "Masculino" },
		{ id: "NB", nome: "Não-binário" },
	];
	estadocivilList = [
		{ id: "A", nome: "Amasiado(a)" },
		{ id: "C", nome: "Casado(a)" },
		{ id: "D", nome: "Divorciado(a)" },
		{ id: "P", nome: "Separado(a)" },
		{ id: "S", nome: "Solteiro(a)" },
		{ id: "U", nome: "União estável" },
		{ id: "V", nome: "Viúvo(a)" },
	];
	listaEstadoUf = [
		{ codigo: "AC", descricao: "Acre" },
		{ codigo: "AL", descricao: "Alagoas" },
		{ codigo: "AP", descricao: "Amapá" },
		{ codigo: "AM", descricao: "Amazonas" },
		{ codigo: "BH", descricao: "Bahia" },
		{ codigo: "CE", descricao: "Ceará" },
		{ codigo: "DF", descricao: "Distrito Federal" },
		{ codigo: "ES", descricao: "Espírito Santo" },
		{ codigo: "GO", descricao: "Goiás" },
		{ codigo: "MA", descricao: "Maranhão" },
		{ codigo: "MT", descricao: "Mato Grosso" },
		{ codigo: "MS", descricao: "Mato Grosso do Sul" },
		{ codigo: "MG", descricao: "Minas Gerais" },
		{ codigo: "PA", descricao: "Pará" },
		{ codigo: "PB", descricao: "Paraíba" },
		{ codigo: "PR", descricao: "Paraná" },
		{ codigo: "PE", descricao: "Pernambuco" },
		{ codigo: "PI", descricao: "Piauí" },
		{ codigo: "RJ", descricao: "Rio de Janeiro" },
		{ codigo: "RN", descricao: "Rio Grande do Norte" },
		{ codigo: "RS", descricao: "Rio Grande do Sul" },
		{ codigo: "RO", descricao: "Rondônia" },
		{ codigo: "RR", descricao: "Roraima" },
		{ codigo: "SC", descricao: "Santa Catarina" },
		{ codigo: "SP", descricao: "São Paulo" },
		{ codigo: "SE", descricao: "Sergipe" },
		{ codigo: "TO", descricao: "Tocantins" },
	];
	cidadeUrl;
	estadoUrl;
	paisUrl = this.restService.buildFullUrlPessoaMs("pais");
	paisList = [];
	estadoList = [];
	cidadeList = [];
	campos = [];
	validar = true;
	profissoesUrl = this.restService.buildFullUrlPessoaMs(
		"alunoColaboradorUsuario/profissoes"
	);
	grauInstrucaoUrl = this.restService.buildFullUrlPessoaMs(
		"alunoColaboradorUsuario/grausInstrucao"
	);
	clientClassificacaoUrl = this.restService.buildFullUrlPessoaMs(
		"alunoColaboradorUsuario/classificacoesClientes"
	);
	categories$ = this.acessoCatracaService
		.getCategorias()
		.pipe(catchError(() => of([])));
	permissaoCliente2_22: any;
	textoRestricao = "";

	@ViewChild("inputNomeRef", { static: false }) inputNomeRef: any;
	@ViewChild("selectProfissao", { static: false }) selectProfissao: any;
	@ViewChild("selectEstado", { static: false }) selectEstado: any;
	@ViewChild("selectCidade", { static: false }) selectCidade: any;
	@ViewChild("inputTelefone", { static: false }) inputTelefone: any;

	constructor(
		private router: Router,
		private fb: FormBuilder,
		private pactoModal: ModalService,
		private cepApi: PactoApiCepService,
		public sessionService: SessionService,
		private toastrService: ToastrService,
		private restService: RestService,
		private snotifyService: SnotifyService,
		private acessoCatracaService: AcessoCatracaService,
		private pessoaService: AlunoColaboradorUsuarioService,
		private readonly adicionarClienteService: AdicionarClienteService,
		private cd: ChangeDetectorRef
	) {
		this.form = this.fb.group({});
	}

	ngOnInit(): void {
		if (!this.adicionarClienteService.empresaSelecionada) {
			this.adicionarClienteService.empresaSelecionada = Number(
				this.sessionService.empresaId
			);
		}
		if (!this.adicionarClienteService.clienteSelecionado) {
			this.adicionarClienteService.clienteSelecionado = {};
		}
		this.pessoaService.getConfiguracoesFormulario().subscribe(
			(response) => {
				this.processarCampos(response.configuracaoSistemaCadastroClienteDTO);
				this.ordenarCampos();
				this.criarFormulario();
				this.campos.forEach((campo) => {
					const nomesRespFinanceiro = [
						"nomerespfinanceiro",
						"cpfrespfinanceiro",
						"rgrespfinanceiro",
						"emailrespfinanceiro",
					];

					if (
						nomesRespFinanceiro.includes(campo._nome) &&
						this.mostrarCheckCadastroResponsavel
					) {
						campo.desabilitado = true;
						campo.obrigatorio = false;

						const control = this.form.get(campo._nome);
						if (control) {
							control.disable({ emitEvent: false });
						}
					}
				});
				this.onChangeDataNasc();
				this.onChangeCpf();
				this.cd.detectChanges();
				if (this.adicionarClienteService.clienteSelecionado) {
					this.patchFormValues(this.adicionarClienteService.clienteSelecionado);
				}
				this.populateEstadoCidadeUrl();
				this.permissaoCliente2_22 =
					this.sessionService.perfilUsuarioAdm.perfilUsuario.recursos.find(
						(r) => r.referenciaRecurso === "2.22"
					);
			},
			(error) => {},
			() => {
				this.focusOnNome();
			}
		);
	}

	private populateEstadoCidadeUrl() {
		if (this.form.get("pais")) {
			this.form.get("pais").valueChanges.subscribe((pais) => {
				if (pais) {
					this.estadoUrl = this.restService.buildFullUrlPessoaMs(
						`estado/${pais}`
					);
					this.form.get("estado").setValue(null);
					this.form.get("cidade").setValue(null);
					this.selectCidade.options = [];
					this.cd.detectChanges();
					this.selectEstado.search("", "", "");
					this.cd.detectChanges();
				}
			});
		}
		if (this.form.get("estado")) {
			this.form.get("estado").valueChanges.subscribe((estado) => {
				if (estado) {
					this.cidadeUrl = this.restService.buildFullUrlPessoaMs(
						`cidade/${estado}`
					);
					this.form.get("cidade").setValue(null);
					this.cd.detectChanges();
					this.selectCidade.search("", "", "");
					this.cd.detectChanges();
				}
			});
		}
	}

	private onChangeCpf() {
		if (!this.form.get("cpf")) {
			return;
		}
		this.form
			.get("cpf")
			.valueChanges.pipe(pairwise())
			.subscribe(([prevValue, value]) => {
				if (value && prevValue && value !== prevValue) {
					if (value.length === 11) {
						setTimeout(() => {
							this.validarAlunoSemelhante(false);
						}, 500);
						setTimeout(() => {
							this.consultarRestricao();
						}, 500);
					}
				}
			});
	}

	consultarRestricao(): void {
		this.textoRestricao = "";
		const cpf: string = String(
			this.form.get("cpf").value ? this.form.get("cpf").value : ""
		).replace(/\D/g, "");
		if (!cpf || cpf.length !== 11) {
			return;
		}
		this.adicionarClienteService
			.consultarRestricao(
				Number(this.sessionService.empresaId),
				"",
				cpf,
				"",
				""
			)
			.subscribe(
				(resp) => {
					this.textoRestricao = resp;
					this.cd.detectChanges();
				},
				({ error = {} }) => {
					const message =
						error.meta && error.meta.message
							? error.meta.message
							: "Erro ao consultar restrição";
					this.snotifyService.error(message);
					this.cd.detectChanges();
				}
			);
	}

	private onChangeDataNasc() {
		if (!this.form.get("datanascimento")) {
			return;
		}
		this.form.get("datanascimento").valueChanges.subscribe((v) => {
			const today = moment();
			const age = today.diff(v, "years");
			if (age < 18) {
				this.apresentarCampo("nomemaeouresponsavel", true, true);
				this.apresentarCampo("cpfmae", true, true);
				this.apresentarCampo("rgmae", true, true);
				this.apresentarCampo("contatoemergencia", true, true);
			} else {
				this.voltarCampoOriginal("nomemaeouresponsavel");
				this.voltarCampoOriginal("cpfmae");
				this.voltarCampoOriginal("rgmae");
				this.voltarCampoOriginal("contatoemergencia");
			}
			this.cd.detectChanges();
		});
	}

	private patchFormValues(data: any): void {
		Object.keys(data).forEach((key) => {
			if (this.form.get(key)) {
				this.form.get(key).patchValue(data[key]);
			}
		});
		if (
			this.inputTelefone &&
			this.form.get("telefone") &&
			this.adicionarClienteService.clienteSelecionado.telefoneddi &&
			(this.adicionarClienteService.clienteSelecionado.telefonenumero ||
				this.adicionarClienteService.clienteSelecionado.telefone)
		) {
			const ddi = this.adicionarClienteService.clienteSelecionado.telefoneddi;
			let numero =
				this.adicionarClienteService.clienteSelecionado.telefonenumero;
			if (!numero) {
				numero = this.adicionarClienteService.clienteSelecionado.telefone;
			}
			if (numero) {
				numero = numero.replace(/[^0-9]/g, "");
			}
			this.inputTelefone.selectCountry({ code: ddi ? Number(ddi) : 55 });
			this.inputTelefone.phoneControl.setValue(numero);
		}
	}

	private processarCampos(campos: any[]): void {
		const nomesExcluidos = new Set<string>([
			"CNPJ",
			"CNPJ Sesi Indústria",
			"Matrícula",
			"Descrição Telefone",
		]);

		this.campos = campos.filter(
			(item) =>
				!nomesExcluidos.has(item.nome) &&
				(item.visitante)
		);

		const camposFixos = [
			{ nome: "RNE", obrigatorio: false },
			{ nome: "Passaporte", obrigatorio: false },
			{ nome: "estrangeiro", obrigatorio: false },
			{ nome: "checkcadastroresponsavel", obrigatorio: false },
		];

		camposFixos.forEach((campoFixo) => {
			if (!this.campos.some((campo) => campo.nome === campoFixo.nome)) {
				this.campos.push({
					nome: campoFixo.nome,
					obrigatorio: campoFixo.obrigatorio,
					mostrar: true,
					visitante: true,
					validarCatraca: false,
				});
			}
		});

		const camposObrigatorios = ["nome"];

		camposObrigatorios.forEach((campoNome) => {
			const indexCampoExistente = this.campos.findIndex(
				(campo) => campo.nome.toUpperCase() === campoNome.toUpperCase()
			);

			if (!this.campos[indexCampoExistente]) {
				this.campos.push({
					nome: campoNome,
					obrigatorio: true,
					mostrar: true,
					visitante: true,
					validarCatraca: false,
				});
			} else {
				this.campos[indexCampoExistente].obrigatorio = true;
				this.campos[indexCampoExistente].mostrar = true;
			}
		});

		this.campos = this.campos.map((campo) => {
			const _nome = this.removerAcentos(campo.nome)
				.replace(/[^a-zA-Z0-9]/g, "")
				.toLowerCase();
			return {
				...campo,
				_nome,
				label: this.getLabel(_nome),
				mask: this.getMask(_nome),
				maxlength: this.getMaxLength(_nome),
				mostrarOriginal: campo.mostrar,
				obrigatorioOriginal: campo.obrigatorio,
				usaValidadorEmail: _nome === "email" || _nome === "emailrespfinanceiro",
			};
		});

		let campoRG;
		this.campos.forEach((campo) => {
			if (campo._nome === "rg") {
				campoRG = campo;
			}
		});

		if (campoRG) {
			const rgorgao = {
				nome: "RG ORGÃO",
				_nome: "rgorgao",
				label: this.getLabel("rgorgao"),
				mostrar: false,
				obrigatorio: campoRG.obrigatorio,
				pendente: campoRG.pendente,
				visitante: campoRG.visitante,
				validarCatraca: campoRG.validarCatraca,
				mask: null,
				mostrarOriginal: campoRG.mostrar,
				obrigatorioOriginal: campoRG.obrigatorio,
			};
			const rgurf = {
				nome: "RG UF",
				_nome: "rguf",
				label: this.getLabel("rguf"),
				mostrar: false,
				obrigatorio: campoRG.obrigatorio,
				pendente: campoRG.pendente,
				visitante: campoRG.visitante,
				validarCatraca: campoRG.validarCatraca,
				mask: null,
				mostrarOriginal: campoRG.mostrar,
				obrigatorioOriginal: campoRG.obrigatorio,
			};
			this.campos.push(rgorgao);
			this.campos.push(rgurf);
		}
	}

	onChangeEstrangeiro(value: boolean): void {
		if (this.isEstrangeiro) {
			this.form.get("cpf").disable();
			this.form.get("cpf").setValue("");
			this.form.get("cpf").clearValidators();
			this.apresentarCampo("cpf", true, false);
			this.apresentarCampo("rne", true, true);
			this.apresentarCampo("passaporte", true, false);
		} else {
			this.form.get("cpf").enable();
			this.form.get("rne").setValue("");
			this.form.get("passaporte").setValue("");
			this.voltarCampoOriginal("cpf");
			this.apresentarCampo("rne", false, false);
			this.apresentarCampo("passaporte", false, false);
		}
		this.cd.detectChanges();
	}

	onChangeResponsavel(value: boolean): void {
		const nomesRespFinanceiro = [
			"rgrespfinanceiro",
			"nomerespfinanceiro",
			"emailrespfinanceiro",
			"cpfrespfinanceiro",
		];

		const camposExistentes = this.campos.filter((c) =>
			nomesRespFinanceiro.includes(c._nome)
		);

		camposExistentes.forEach((campo) => {
			const control = this.form.get(campo._nome);
			if (!control) return;

			if (value) {
				control.enable();
				campo.obrigatorio = campo.obrigatorioOriginal;
				if (campo.obrigatorio) {
					const validators = campo.usaValidadorEmail
						? [Validators.required, Validators.email]
						: [Validators.required];
					control.setValidators(validators);
				} else {
					control.clearValidators();
				}
				campo.mostrar = campo.mostrarOriginal;
			} else {
				control.clearValidators();
				control.setValue("");
				control.disable();
				campo.obrigatorio = false;
				campo.mostrar = campo.mostrarOriginal;
			}

			control.updateValueAndValidity({ emitEvent: false });
		});

		this.cd.detectChanges();
	}

	voltarCampoOriginal(campo) {
		this.processarCampo(campo, false, false, true);
	}

	apresentarCampo(campo, mostrar, obrigatorio) {
		this.processarCampo(campo, mostrar, obrigatorio, false);
	}

	processarCampo(campo, mostrar, obrigatorio, voltarOriginal) {
		this.campos.forEach((v) => {
			if (v._nome.toUpperCase() === campo.toUpperCase()) {
				if (voltarOriginal) {
					v.mostrar = v.mostrarOriginal;
					v.obrigatorio = v.obrigatorioOriginal;
				} else {
					v.mostrar = mostrar;
					v.obrigatorio = obrigatorio;
				}
				if (v.usaValidadorEmail) {
					this.form.get(v._nome).setValidators(Validators.email);
				}
				if (obrigatorio) {
					this.form.get(v._nome).setValidators(Validators.required);
				} else {
					this.form.get(v._nome).clearValidators();
				}
				this.form.get(v._nome).updateValueAndValidity();
			}
		});
	}

	private removerAcentos(texto: string): string {
		return texto.normalize("NFD").replace(/[̀-ͯ]/g, "");
	}

	private ordenarCampos(): void {
		this.campos.sort((a, b) => {
			const indexA = CampoOrdem[a._nome as keyof typeof CampoOrdem];
			const indexB = CampoOrdem[b._nome as keyof typeof CampoOrdem];
			return (
				(indexA !== undefined ? indexA : 999) -
				(indexB !== undefined ? indexB : 999)
			);
		});
	}

	private criarFormulario(): void {
		const formControls = this.campos.reduce((controls, campo) => {
			if (campo._nome === "email" || campo._nome === "emailrespfinanceiro") {
				controls[campo._nome] = new FormControl(
					null,
					campo.obrigatorio
						? [Validators.required, Validators.email]
						: [Validators.email]
				);
			} else {
				controls[campo._nome] = new FormControl(
					null,
					campo.obrigatorio ? Validators.required : []
				);
			}
			return controls;
		}, {} as { [key: string]: FormControl });
		this.form = this.fb.group(formControls);
	}

	buscaCep(): void {
		const cep = this.form.value.cep.replace("-", "");
		this.cepApi.buscaCEP(this.sessionService.chave, cep).subscribe({
			next: (response) => {
				if (response.content) {
					this.selectEstado.options = [
						{
							descricao: response.content.estado_nome,
							codigo: response.content.estado_codigo,
						},
					];
					this.selectCidade.options = [
						{
							nome: response.content.cidade_nome,
							codigo: response.content.cidade_codigo,
						},
					];
					this.form.patchValue({
						pais: response.content.pais_codigo,
						estado: response.content.estado_codigo,
						cidade: response.content.cidade_codigo,
						bairro: response.content.bairro,
						endereco: response.content.endereco,
					});
					const NUMERO_INPUT = window.document.getElementById("numero"); // numero cai no grupo de controls genericos, unica forma de dar foco para ele foi pelo document
					if (!!NUMERO_INPUT) {
						NUMERO_INPUT.focus();
					}
					this.cd.detectChanges();
				}
			},
			error: (error) => {
				this.toastrService.error(error.error.meta.message);
				this.form.patchValue({
					cep: null,
					pais: null,
					estado: null,
					cidade: null,
					bairro: null,
					endereco: null,
				});
				this.form.updateValueAndValidity();
			},
		});
	}

	buscaEndereco(): void {
		const dialogRef = this.pactoModal.open(
			"Consulta de CEP",
			ModalConsultaEnderecoComponent,
			PactoModalSize.MEDIUM
		);
		dialogRef.componentInstance.update.subscribe((respModal) => {
			if (respModal.enderecoCep && respModal.enderecoCep.length > 0) {
				this.form.get("cep").setValue(respModal.enderecoCep);
				this.buscaCep();
			}
		});
	}	

	avancar(): void {
		this.form.markAllAsTouched();
		if (!this.form.valid) {
			this.toastrService.warning("Preencha os campos obrigatórios.");
			return;
		}

		if (!this.validar) {
			this.router.navigateByUrl("/adm/adicionar-cliente/boletim");
		} else {
			this.validarAlunoSemelhante(true);
		}
	}

	validarAlunoSemelhante(avancar) {
		const dados = this.filterFormValues(this.form.value);
		if (this.adicionarClienteService.clienteSelecionado.indicado) {
			dados.indicado = this.adicionarClienteService.clienteSelecionado.indicado;
		}
		if (this.adicionarClienteService.clienteSelecionado.passivo) {
			dados.passivo = this.adicionarClienteService.clienteSelecionado.passivo;
		}
		this.adicionarClienteService.clienteSelecionado = dados;
		this.adicionarClienteService.clienteSelecionado.empresa = {
			codigo: Number(this.sessionService.empresaId),
		};
		this.adicionarClienteService.clienteSelecionado.usuario = {
			codigo: Number(this.sessionService.codUsuarioZW),
		};
		if (this.inputTelefone) {
			this.adicionarClienteService.clienteSelecionado.telefoneddi =
				this.inputTelefone.selectedCountryCode;
			this.adicionarClienteService.clienteSelecionado.telefonenumero =
				this.inputTelefone.phoneControl.value;
			this.adicionarClienteService.clienteSelecionado.telefonegeral =
				this.adicionarClienteService.clienteSelecionado.telefone;
			// como envia o ddi separado então no campo numero só enviar o numero sem o ddi
			this.adicionarClienteService.clienteSelecionado.telefone =
				this.inputTelefone.phoneControl.value;
			if (
				this.adicionarClienteService.clienteSelecionado.telefoneddi &&
				this.adicionarClienteService.clienteSelecionado.telefonenumero &&
				this.adicionarClienteService.clienteSelecionado.telefoneddi === 55 &&
				this.adicionarClienteService.clienteSelecionado.telefonenumero.length >
					0 &&
				this.adicionarClienteService.clienteSelecionado.telefonenumero.length <
					10
			) {
				this.snotifyService.error(
					"O número do telefone celular está inválido",
					{
						timeout: 5000,
						bodyMaxLength: 300,
					}
				);
				return;
			}
		}

		this.adicionarClienteService
			.validarExisteCliente(this.adicionarClienteService.clienteSelecionado)
			.subscribe(
				(respValida) => {
					if (respValida.clientes && respValida.clientes.length > 0) {
						const dialogRef = this.pactoModal.open(
							"Clientes semelhantes",
							ModalClienteSemelhanteComponent,
							PactoModalSize.LARGE
						);
						dialogRef.componentInstance.apresentarIgnorar =
							respValida.apresentarIgnorarClienteDuplicado;
						dialogRef.componentInstance.clientes = respValida.clientes;
						dialogRef.componentInstance.update.subscribe((respModal) => {
							if (respModal.operacao === "RESPONSAVEL") {
								this.adicionarClienteService.clienteSelecionado.pessoaResponsavel =
									respModal.cliente.pessoaDTO;
								this.validar = false;
							} else if (respModal.operacao === "EDITAR") {
								this.router.navigateByUrl(
									"/pessoas/perfil-v2/" + respModal.cliente.matricula
								);
							} else if (respModal.operacao === "IGNORAR") {
								this.validar = false;
								if (avancar) {
									this.router.navigateByUrl("/adm/adicionar-cliente/boletim");
								}
							} else if (respModal.operacao === "INFORMAR_OUTRO_CPF") {
								this.form.get("cpf").setValue("");
								this.validar = true;
							} else if (respModal.operacao === "TRANSFERIR") {
								const clienteCadastro = {
									cliente: respModal.cliente.codigo,
								};

								this.adicionarClienteService
									.validarTrocaEmpresa(clienteCadastro)
									.subscribe(
										(respTroca) => {
											const dialogRef2 = this.pactoModal.open(
												"Transferir cliente de empresa",
												ModalTransferirClienteEmpresaComponent,
												PactoModalSize.LARGE
											);
											dialogRef2.componentInstance.dados = respTroca;
											dialogRef2.componentInstance.update.subscribe(
												(respModalTr) => {
													const body = {
														cliente: respModal.cliente.codigo,
														empresaTrocaEmpresa: Number(
															this.sessionService.empresaId
														),
														alterarConvenioCobrancaTrocaEmpresa:
															respModalTr.alterarConvenioCobrancaTrocaEmpresa,
														convenioCobrancaTrocaEmpresa:
															respModalTr.convenioCobranca,
													};

													this.adicionarClienteService.gravar(body).subscribe(
														(respGravar: any) => {
															this.router.navigateByUrl(
																"/pessoas/perfil-v2/" + respGravar.matricula
															);
														},
														({ error = {} }) => {
															console.log(error);
															this.snotifyService.error(error.meta.message);
														}
													);
												}
											);
										},
										({ error = {} }) => {
											console.log(error);
											this.snotifyService.error(error.meta.message);
										}
									);
							}
						});
					} else if (avancar) {
						this.router.navigateByUrl("/adm/adicionar-cliente/boletim");
					}
				},
				({ error = {} }) => {
					console.log(error);
					this.snotifyService.error(error.meta.message);
				}
			);
	}

	private filterFormValues(formValue: any): any {
		return Object.keys(formValue)
			.filter((key) => formValue[key] !== null && formValue[key] !== "")
			.reduce((obj, key) => {
				obj[key] = formValue[key];
				return obj;
			}, {});
	}

	getLabel(nome: string): string {
		return CampoLabelEnum[nome as keyof typeof CampoLabelEnum] || nome;
	}

	getMask(nome: string): string {
		return (
			(CampoMaskEnum[
				nome as keyof typeof CampoMaskEnum
			] as unknown as string) || ""
		);
	}

	getMaxLength(nome: string): string {
		return CampoMaxLengthEnum[nome as keyof typeof CampoMaxLengthEnum] || "200";
	}

	permitirSomenteTexto(event: KeyboardEvent): void {
		const char = event.key;
		const isLetter = /^[a-zA-Z\u00C0-\u017F\s]$/.test(char); // Permite letras, espaços e caracteres acentuados

		if (!isLetter) {
			event.preventDefault();
		}
	}

	get enableConsultaCep(): boolean {
		return this.form.get("cep").valid && !!this.form.get("cep").value;
	}

	get isEstrangeiro(): boolean {
		return this.form && this.form.get("estrangeiro")
			? this.form.get("estrangeiro").value
			: false;
	}

	get isEstrangeiroVisible(): boolean {
		return this.form.get("estrangeiro").value === true;
	}

	voltar() {
		this.router.navigateByUrl("/adm/adicionar-cliente");
	}

	selectBuilder: SelectFilterParamBuilder = (term) => {
		return {
			filters: JSON.stringify({
				quicksearchValue: term,
				quicksearchFields: ["descricao"],
			}),
		};
	};

	paisParamBuilder: SelectFilterParamBuilder = (term) => {
		return {
			filters: JSON.stringify({
				quicksearchValue: term,
				quicksearchFields: ["nome"],
			}),
		};
	};

	estadoParamBuilder: SelectFilterParamBuilder = (term) => {
		return {
			filters: JSON.stringify({
				quicksearchValue: term,
				quicksearchFields: ["descricao"],
			}),
		};
	};

	cidadeParamBuilder: SelectFilterParamBuilder = (term) => {
		return {
			filters: JSON.stringify({
				quicksearchValue: term,
				quicksearchFields: ["descricao"],
			}),
		};
	};

	cadastrarProfissao() {
		if (!this.permiteCadastrarProfissao()) {
			this.snotifyService.error(
				'Você não possui a permissão "2.22 - Profissões"',
				{
					timeout: 5000,
					bodyMaxLength: 300,
				}
			);
			return;
		}
		const dialogRef = this.pactoModal.open(
			"Adicionar profissão",
			ModalAddProfissaoComponent,
			PactoModalSize.MEDIUM
		);
		dialogRef.componentInstance.update.subscribe((res) => {
			this.selectProfissao.search("", "", "");
			if (res.content) {
				this.form.get("profissao").setValue(res.content.codigo);
			}
			this.cd.detectChanges();
		});
	}

	permiteCadastrarProfissao(): any {
		this.permissaoCliente2_22 =
			this.sessionService.perfilUsuarioAdm.perfilUsuario.recursos.find(
				(r) => r.referenciaRecurso === "2.22"
			);
		const permition = this.permissaoCliente2_22;
		const isPermited =
			permition &&
			permition.tipoPermissoes.find(
				(tp) =>
					tp === PerfilRecursoPermissoTipo.INCLUIR ||
					tp === PerfilRecursoPermissoTipo.TOTAL_EXCETO_EXCLUIR ||
					tp === PerfilRecursoPermissoTipo.TOTAL
			);
		const retor = isPermited !== undefined && isPermited;
		return retor;
	}

	focusOnNome() {
		this.inputNomeRef.nativeElement.focus();
	}

	nomeResponsavel(): string {
		if (
			this.adicionarClienteService.clienteSelecionado &&
			this.adicionarClienteService.clienteSelecionado.pessoaResponsavel &&
			this.adicionarClienteService.clienteSelecionado.pessoaResponsavel
				.codigo &&
			this.adicionarClienteService.clienteSelecionado.pessoaResponsavel.codigo >
				0
		) {
			return this.adicionarClienteService.clienteSelecionado.pessoaResponsavel
				.nome;
		} else {
			return "";
		}
	}

	removerResponsavel() {
		this.validar = true;
		this.adicionarClienteService.clienteSelecionado.pessoaResponsavel = {};
	}

	get mostrarCheckCadastroResponsavel(): boolean {
		if (!this.campos || !this.campos.length) {
			return false;
		}

		const nomesRespFinanceiro = [
			"nomerespfinanceiro",
			"cpfrespfinanceiro",
			"rgrespfinanceiro",
			"emailrespfinanceiro",
		];

		return this.campos.some(
			(campo) => nomesRespFinanceiro.includes(campo._nome) && campo.mostrar
		);
	}
}
