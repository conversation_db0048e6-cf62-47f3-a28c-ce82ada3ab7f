.flex-container {
	height: 40px;
	display: flex;
	flex-direction: row;
	margin-top: auto;
	width: auto;
}

.highlight-text {
	background-color: #f8f9fa; /* Cinza claro */
	padding: 2px 6px;
	border-radius: 4px;
	font-weight: bold;
	font-family: monospace;
}

.icon-prof {
	cursor: pointer;
	color: #1e60fa;
	font-size: 20px;
	margin-top: 50px;
}

.div-btn-superior {
	padding-bottom: 30px;
}

.nome-responsavel {
}

.div-pessoa-responsavel {
	display: flex;
	padding-top: 20px;
	align-items: center;
	gap: 20px;
	justify-content: start;
}

::ng-deep .counter {
	display: none;
}

::ng-deep .ds3-field-label {
	margin-bottom: 0px !important;
}

.row {
	margin-bottom: 16px;
}

.div-restricao {
	width: 100%;
	margin: 12px 0;
	padding: 6px 12px;
	color: #e10505;
	background: #fee6e6;
	border: 1px solid #fee6e6;
	box-sizing: border-box;
	border-radius: 8px;
	font-weight: 400;
	line-height: 17.5px;
	font-family: Poppins;
	text-align: justify;
	display: flex;
	align-items: center;
}

.text-restricao {
	font-family: Poppins;
	font-size: 14px;
	font-weight: 400;
	line-height: 17.5px;
	text-align: left;
	text-underline-position: from-font;
	text-decoration-skip-ink: none;
	color: #E10505 !important;
}

.clique-consulta-endereco {
  cursor: pointer;
  color: #1e60fa;
}

.div-btn-buscar-cep {
  padding-top: 5px;
}

.div-cep-geral {
  margin-top: 30px;
}

.btn-buscar-cep {
  max-width: none !important;
  width: auto !important;
}