<adm-layout
	i18n-pageTitle="@@adicionar-cliente-cadastro:title"
	pageTitle="Adicionar cliente"
	i18n-modulo="@@adicionar-cliente-cadastro:modulo"
	modulo="Administrativo"
	[formVersaoBeta]="'https://pacto.in/feedback-adicao-de-novo-cliente'"
	(goBack)="voltar()"
	appStickyFooter>
	<pacto-cat-card-plain>
		<ng-container>
			<form [formGroup]="form">
				<div *ngFor="let campo of campos; let index = index">
					<div *ngIf="campo.mostrar" class="row">
						<ng-container [ngSwitch]="campo._nome">
							<ng-container *ngSwitchCase="'cpf'">
								<ds3-form-field class="col-6">
									<ds3-field-label>
										CPF
										<span *ngIf="campo.obrigatorio">*</span>
										<span *ngIf="campo.pendente">**</span>
									</ds3-field-label>
									<input
										ds3Input
										formControlName="cpf"
										type="text"
										id="add-cliente-cadastro-cpf"
										[mask]="'000.000.000-00'" />
									<ds3-helper-message
										*ngIf="
											form.controls['cpf'].touched &&
											form.controls['cpf'].hasError('required')
										">
										É obrigatório preencher este campo
									</ds3-helper-message>
									<ds3-helper-message
										*ngIf="
											form.controls['cpf'].dirty &&
											form.controls['cpf'].hasError('mask')
										">
										cpf inválido
									</ds3-helper-message>
								</ds3-form-field>
								<ds3-form-field class="col-1" style="padding-top: 50px">
									<ds3-checkbox
										id="add-cliente-cadastro-estrangeiro"
										(ngModelChange)="onChangeEstrangeiro($event)"
										[formControl]="form.get('estrangeiro')"
										ds3Input>
										Estrangeiro
									</ds3-checkbox>
								</ds3-form-field>
								<ds3-form-field class="col-5" style="padding-top: 20px">
									<div class="div-pessoa-responsavel">
										<div
											class="nome-responsavel"
											*ngIf="nomeResponsavel().length > 0">
											{{ nomeResponsavel() }}
										</div>
										<button
											id="btn-remover-pessoa-responsavel"
											*ngIf="nomeResponsavel().length > 0"
											class="flex-container"
											(click)="removerResponsavel()"
											ds3-outlined-button>
											REMOVER RESPONSÁVEL
										</button>
									</div>
								</ds3-form-field>
								<ds3-form-field *ngIf="textoRestricao" class="col-12">
									<section class="d-flex align-items-center div-restricao mb-3">
										<i class="pct pct-info mr-2"></i>
										<span class="text-restricao">
											{{ textoRestricao }}
										</span>
									</section>
								</ds3-form-field>
							</ng-container>

							<ng-container *ngSwitchCase="'nome'">
								<ds3-form-field class="col-6">
									<ds3-field-label>
										Nome completo
										<span *ngIf="campo.obrigatorio">*</span>
										<span *ngIf="campo.pendente">**</span>
									</ds3-field-label>
									<input
										ds3Input
										#inputNomeRef
										id="add-cliente-cadastro-nome"
										formControlName="nome"
										type="text" />
									<ds3-helper-message
										*ngIf="
											form.controls['nome'].touched &&
											form.controls['nome'].hasError('required')
										">
										É obrigatório preencher este campo
									</ds3-helper-message>
								</ds3-form-field>
							</ng-container>

							<ng-container *ngSwitchCase="'rne'">
								<ng-container *ngIf="isEstrangeiroVisible">
									<ds3-form-field class="col-6">
										<ds3-field-label>
											RNE
											<span *ngIf="campo.obrigatorio">*</span>
											<span *ngIf="campo.pendente">**</span>
										</ds3-field-label>
										<input
											ds3Input
											id="add-cliente-cadastro-rne"
											formControlName="rne"
											type="text" />
										<ds3-helper-message
											*ngIf="
												form.controls['rne'].touched &&
												form.controls['rne'].hasError('required')
											">
											É obrigatório preencher este campo
										</ds3-helper-message>
									</ds3-form-field>
								</ng-container>
							</ng-container>

							<ng-container *ngSwitchCase="'passaporte'">
								<ng-container *ngIf="isEstrangeiroVisible">
									<ds3-form-field class="col-6">
										<ds3-field-label>
											Passaporte
											<span *ngIf="campo.obrigatorio">*</span>
											<span *ngIf="campo.pendente">**</span>
										</ds3-field-label>
										<input
											ds3Input
											id="add-cliente-cadastro-passaporte"
											formControlName="passaporte"
											type="text" />
										<ds3-helper-message
											*ngIf="
												form.controls['passaporte'].touched &&
												form.controls['passaporte'].hasError('required')
											">
											É obrigatório preencher este campo
										</ds3-helper-message>
									</ds3-form-field>
								</ng-container>
							</ng-container>

							<ng-container *ngSwitchCase="'checkcadastroresponsavel'">
								<ng-container *ngIf="mostrarCheckCadastroResponsavel">
									<ds3-diviser class="mt-4 mb-2"></ds3-diviser>
									<ds3-form-field class="col-12">
										<ds3-checkbox
											id="check-cadastro-responsavel"
											[formControl]="form.get('checkcadastroresponsavel')"
											(ngModelChange)="onChangeResponsavel($event)"
											ds3Input>
											Cadastrar responsável financeiro
										</ds3-checkbox>
									</ds3-form-field>
								</ng-container>
							</ng-container>

							<ng-container *ngSwitchCase="'cpfrespfinanceiro'">
								<ds3-form-field class="col-6">
									<ds3-field-label>
										CPF do responsável financeiro
										<span *ngIf="campo.obrigatorio">*</span>
										<span *ngIf="campo.pendente">**</span>
									</ds3-field-label>
									<input
										ds3Input
										formControlName="cpfrespfinanceiro"
										type="text"
										id="add-cliente-cadastro-cpfrespfinanceiro"
										[mask]="'000.000.000-00'" />
									<ds3-helper-message
										*ngIf="
											form.controls['cpfrespfinanceiro'].touched &&
											form.controls['cpfrespfinanceiro'].hasError('required')
										">
										É obrigatório preencher este campo
									</ds3-helper-message>
									<ds3-helper-message
										*ngIf="
											form.controls['cpfrespfinanceiro'].dirty &&
											form.controls['cpfrespfinanceiro'].hasError('mask')
										">
										cpf inválido
									</ds3-helper-message>
								</ds3-form-field>
							</ng-container>

							<ng-container *ngSwitchCase="'rgrespfinanceiro'">
								<ds3-form-field class="col-6">
									<ds3-field-label>
										RG do responsável financeiro
										<span *ngIf="campo.obrigatorio">*</span>
										<span *ngIf="campo.pendente">**</span>
									</ds3-field-label>
									<input
										ds3Input
										formControlName="rgrespfinanceiro"
										type="text"
										id="add-cliente-cadastro-rgrespfinanceiro"
										[mask]="'0*.0*.0*-0*'" />
									<ds3-helper-message
										*ngIf="
											form.controls['rgrespfinanceiro'].touched &&
											form.controls['rgrespfinanceiro'].hasError('required')
										">
										É obrigatório preencher este campo
									</ds3-helper-message>
									<ds3-helper-message
										*ngIf="
											form.controls['rgrespfinanceiro'].dirty &&
											form.controls['rgrespfinanceiro'].hasError('mask')
										">
										inválido
									</ds3-helper-message>
								</ds3-form-field>
							</ng-container>

							<ng-container *ngSwitchCase="'telefone'">
								<ds3-diviser class="mt-4 mb-2"></ds3-diviser>
								<ds3-form-field class="col-6">
									<ds3-field-label>
										Tel. celular
										<span *ngIf="campo.obrigatorio">*</span>
										<span *ngIf="campo.pendente">**</span>
									</ds3-field-label>
									<ds3-country-phone
										#inputTelefone
										id="add-cliente-cadastro-telefone"
										formControlName="telefone"
										ds3Input></ds3-country-phone>
									<ds3-helper-message
										*ngIf="
											form.controls['telefone'].touched &&
											form.controls['telefone'].hasError('required')
										">
										É obrigatório preencher este campo
									</ds3-helper-message>
								</ds3-form-field>
							</ng-container>

							<ng-container *ngSwitchCase="'cep'">
								<ds3-diviser class="mt-4 mb-2"></ds3-diviser>
								<ds3-form-field class="col-6">
									<ds3-field-label>
										CEP
										<span *ngIf="campo.obrigatorio">*</span>
										<span *ngIf="campo.pendente">**</span>
									</ds3-field-label>
									<input
										ds3Input
										id="add-cliente-cadastro-cep"
										formControlName="cep"
										type="text"
										(keydown.enter)="buscaCep()"
										[mask]="'00000-000'" />
									<ds3-helper-message
										*ngIf="
											form.controls['cep'].touched &&
											form.controls['cep'].hasError('required')
										">
										É obrigatório preencher este campo
									</ds3-helper-message>
									<ds3-helper-message
										*ngIf="
											form.controls['cep'].touched &&
											form.controls['cep'].hasError('mask')
										">
										Campo inválido
									</ds3-helper-message>
								</ds3-form-field>
								<div class="col-6 div-cep-geral">
									<button
										class="flex-container col-2 btn-buscar-cep"
										id="add-cliente-cadastro-cep-buscar"
										(click)="buscaCep()"
										[disabled]="!enableConsultaCep"
										ds3-outlined-button>
										Consultar CEP
									</button>
									<div class="div-btn-buscar-cep">
										Não sabe seu cep?
										<span
											id="add-cliente-cadastro-busca-endereco"
											class="clique-consulta-endereco"
											(click)="buscaEndereco()">Clique aqui!</span>
									</div>
								</div>
							</ng-container>

							<ng-container *ngSwitchCase="'pais'">
								<ds3-form-field class="col-6">
									<ds3-field-label>
										País
										<span *ngIf="campo.obrigatorio">*</span>
										<span *ngIf="campo.pendente">**</span>
									</ds3-field-label>
									<ds3-select
										placeholder="-"
										id="add-cliente-cadastro-pais"
										[addEmptyOption]="true"
										[valueKey]="'codigo'"
										[nameKey]="'nome'"
										[endpointUrl]="paisUrl"
										[paramBuilder]="paisParamBuilder"
										ds3Input
										formControlName="pais"></ds3-select>
									<ds3-helper-message
										*ngIf="
											form.controls['pais'].touched &&
											form.controls['pais'].hasError('required')
										">
										É obrigatório preencher este campo
									</ds3-helper-message>
								</ds3-form-field>
							</ng-container>

							<ng-container *ngSwitchCase="'estado'">
								<ds3-form-field class="col-6">
									<ds3-field-label>
										Estado
										<span *ngIf="campo.obrigatorio">*</span>
										<span *ngIf="campo.pendente">**</span>
									</ds3-field-label>
									<ds3-select
										placeholder="-"
										#selectEstado
										id="add-cliente-cadastro-estado"
										[addEmptyOption]="true"
										[valueKey]="'codigo'"
										[nameKey]="'descricao'"
										[endpointUrl]="estadoUrl"
										[paramBuilder]="estadoParamBuilder"
										ds3Input
										formControlName="estado"></ds3-select>
									<ds3-helper-message
										*ngIf="
											form.controls['estado'].touched &&
											form.controls['estado'].hasError('required')
										">
										É obrigatório preencher este campo
									</ds3-helper-message>
								</ds3-form-field>
							</ng-container>

							<ng-container *ngSwitchCase="'cidade'">
								<ds3-form-field class="col-6">
									<ds3-field-label>
										Cidade
										<span *ngIf="campo.obrigatorio">*</span>
										<span *ngIf="campo.pendente">**</span>
									</ds3-field-label>
									<ds3-select
										placeholder="-"
										#selectCidade
										id="add-cliente-cadastro-cidade"
										[addEmptyOption]="true"
										[valueKey]="'codigo'"
										[nameKey]="'nome'"
										[endpointUrl]="cidadeUrl"
										[paramBuilder]="cidadeParamBuilder"
										ds3Input
										formControlName="cidade"></ds3-select>
								</ds3-form-field>
								<ds3-helper-message
									*ngIf="
										form.controls['cidade'].touched &&
										form.controls['cidade'].hasError('required')
									">
									É obrigatório preencher este campo
								</ds3-helper-message>
							</ng-container>

							<ng-container *ngSwitchCase="'datanascimento'">
								<ds3-form-field class="col-6">
									<ds3-field-label>
										Data de nascimento
										<span *ngIf="campo.obrigatorio">*</span>
										<span *ngIf="campo.pendente">**</span>
									</ds3-field-label>
									<ds3-input-date
										required
										ds3Input
										id="add-cliente-cadastro-data-nascimento"
										startView="multi-year"
										[control]="form.get(campo._nome)"
										dateType="datepicker"></ds3-input-date>
									<ds3-helper-message
										*ngIf="
											form.controls[campo._nome].touched &&
											form.controls[campo._nome].hasError('required')
										">
										É obrigatório preencher este campo
									</ds3-helper-message>
								</ds3-form-field>
							</ng-container>

							<ng-container *ngSwitchCase="'sexo'">
								<ds3-form-field class="col-6">
									<ds3-field-label>
										Sexo biológico
										<span *ngIf="campo.obrigatorio">*</span>
										<span *ngIf="campo.pendente">**</span>
									</ds3-field-label>
									<ds3-select
										placeholder="-"
										id="add-cliente-cadastro-sexo"
										[nameKey]="'nome'"
										[options]="sexoList"
										[valueKey]="'id'"
										ds3Input
										formControlName="sexo"></ds3-select>
									<ds3-helper-message
										*ngIf="
											form.controls['sexo'].touched &&
											form.controls['sexo'].hasError('required')
										">
										É obrigatório preencher este campo
									</ds3-helper-message>
								</ds3-form-field>
							</ng-container>

							<ng-container *ngSwitchCase="'categoria'">
								<ds3-form-field class="col-6">
									<ds3-field-label>
										Categoria
										<span *ngIf="campo.obrigatorio">*</span>
										<span *ngIf="campo.pendente">**</span>
									</ds3-field-label>
									<ds3-select
										placeholder="-"
										id="add-cliente-cadastro-categoria"
										[valueKey]="'codigo'"
										[nameKey]="'nome'"
										[options]="categories$ | async"
										ds3Input
										formControlName="categoria"></ds3-select>
									<ds3-helper-message
										*ngIf="
											form.controls['categoria'].touched &&
											form.controls['categoria'].hasError('required')
										">
										É obrigatório preencher este campo
									</ds3-helper-message>
								</ds3-form-field>
							</ng-container>

							<ng-container *ngSwitchCase="'genero'">
								<ds3-form-field class="col-6">
									<ds3-field-label>
										Gênero
										<span *ngIf="campo.obrigatorio">*</span>
										<span *ngIf="campo.pendente">**</span>
									</ds3-field-label>
									<ds3-select
										placeholder="-"
										id="add-cliente-cadastro-genero"
										[nameKey]="'nome'"
										[options]="generoList"
										[valueKey]="'id'"
										ds3Input
										formControlName="genero"></ds3-select>
									<ds3-helper-message
										*ngIf="
											form.controls['genero'].touched &&
											form.controls['genero'].hasError('required')
										">
										É obrigatório preencher este campo
									</ds3-helper-message>
								</ds3-form-field>
							</ng-container>

							<ng-container *ngSwitchCase="'profissao'">
								<ds3-form-field class="col-6">
									<ds3-field-label>
										Profissão
										<span *ngIf="campo.obrigatorio">*</span>
										<span *ngIf="campo.pendente">**</span>
									</ds3-field-label>
									<ds3-select
										placeholder="-"
										#selectProfissao
										id="add-cliente-cadastro-profissao"
										[valueKey]="'codigo'"
										[nameKey]="'descricao'"
										[endpointUrl]="profissoesUrl"
										[paramBuilder]="selectBuilder"
										ds3Input
										formControlName="profissao"></ds3-select>
									<ds3-helper-message
										*ngIf="
											form.controls['profissao'].touched &&
											form.controls['profissao'].hasError('required')
										">
										É obrigatório preencher este campo
									</ds3-helper-message>
								</ds3-form-field>

								<span class="icon-prof" (click)="cadastrarProfissao()">
									<i class="pct pct-plus-circle"></i>
								</span>
							</ng-container>

							<ng-container *ngSwitchCase="'graudeinstrucao'">
								<ds3-form-field class="col-6">
									<ds3-field-label>
										Grau de instrução
										<span *ngIf="campo.obrigatorio">*</span>
										<span *ngIf="campo.pendente">**</span>
									</ds3-field-label>
									<ds3-select
										placeholder="-"
										id="add-cliente-cadastro-grau-instrucao"
										[endpointUrl]="grauInstrucaoUrl"
										[valueKey]="'codigo'"
										[nameKey]="'descricao'"
										[paramBuilder]="selectBuilder"
										ds3Input
										formControlName="graudeinstrucao"></ds3-select>
									<ds3-helper-message
										*ngIf="
											form.controls['graudeinstrucao'].touched &&
											form.controls['graudeinstrucao'].hasError('required')
										">
										É obrigatório preencher este campo
									</ds3-helper-message>
								</ds3-form-field>
							</ng-container>

							<ng-container *ngSwitchCase="'estadocivil'">
								<ds3-form-field class="col-6">
									<ds3-field-label>
										Estado civil
										<span *ngIf="campo.obrigatorio">*</span>
										<span *ngIf="campo.pendente">**</span>
									</ds3-field-label>
									<ds3-select
										placeholder="-"
										id="add-cliente-cadastro-estado-civil"
										[nameKey]="'nome'"
										[options]="estadocivilList"
										[valueKey]="'id'"
										ds3Input
										formControlName="estadocivil"></ds3-select>
									<ds3-helper-message
										*ngIf="
											form.controls['estadocivil'].touched &&
											form.controls['estadocivil'].hasError('required')
										">
										É obrigatório preencher este campo
									</ds3-helper-message>
								</ds3-form-field>
							</ng-container>

							<ng-container *ngSwitchCase="'rg'">
								<ds3-form-field class="col-2">
									<ds3-field-label>
										RG
										<span *ngIf="campo.obrigatorio">*</span>
										<span *ngIf="campo.pendente">**</span>
									</ds3-field-label>
									<input
										ds3Input
										id="add-cliente-cadastro-rg"
										[maxlength]="campo.maxlength"
										[formControlName]="'rg'"
										type="text" />
									<ds3-helper-message
										*ngIf="
											form.controls['rg'].touched &&
											form.controls['rg'].hasError('required')
										">
										É obrigatório preencher este campo
									</ds3-helper-message>
								</ds3-form-field>
								<ds3-form-field class="col-2">
									<ds3-field-label>
										Orgão expedidor
										<span *ngIf="campo.obrigatorio">*</span>
										<span *ngIf="campo.pendente">**</span>
									</ds3-field-label>
									<input
										ds3Input
										id="add-cliente-cadastro-rg-orgao"
										[maxlength]="campo.maxlength"
										[formControlName]="'rgorgao'"
										type="text" />
									<ds3-helper-message
										*ngIf="
											form.controls['rgorgao'].touched &&
											form.controls['rgorgao'].hasError('required')
										">
										É obrigatório preencher este campo
									</ds3-helper-message>
								</ds3-form-field>
								<ds3-form-field class="col-2">
									<ds3-field-label>
										UF
										<span *ngIf="campo.obrigatorio">*</span>
										<span *ngIf="campo.pendente">**</span>
									</ds3-field-label>
									<ds3-select
										placeholder="-"
										id="add-cliente-cadastro-rg-uf"
										[nameKey]="'descricao'"
										[options]="listaEstadoUf"
										[valueKey]="'codigo'"
										ds3Input
										formControlName="rguf"></ds3-select>
									<ds3-helper-message
										*ngIf="
											form.controls['rguf'].touched &&
											form.controls['rguf'].hasError('required')
										">
										É obrigatório preencher este campo
									</ds3-helper-message>
								</ds3-form-field>
							</ng-container>

							<ng-container *ngSwitchCase="'cpfmae'">
								<ds3-form-field class="col-6">
									<ds3-field-label>
										CPF da mãe ou responsável
										<span *ngIf="campo.obrigatorio">*</span>
										<span *ngIf="campo.pendente">**</span>
									</ds3-field-label>
									<input
										ds3Input
										formControlName="cpfmae"
										type="text"
										id="add-cliente-cadastro-cpfmae"
										[mask]="'000.000.000-00'" />
									<ds3-helper-message
										*ngIf="
											form.controls['cpfmae'].touched &&
											form.controls['cpfmae'].hasError('required')
										">
										É obrigatório preencher este campo
									</ds3-helper-message>
									<ds3-helper-message
										*ngIf="
											form.controls['cpfmae'].dirty &&
											form.controls['cpfmae'].hasError('mask')
										">
										cpf inválido
									</ds3-helper-message>
								</ds3-form-field>
							</ng-container>

							<ng-container *ngSwitchCase="'cpfpai'">
								<ds3-form-field class="col-6">
									<ds3-field-label>
										CPF do pai ou responsável
										<span *ngIf="campo.obrigatorio">*</span>
										<span *ngIf="campo.pendente">**</span>
									</ds3-field-label>
									<input
										ds3Input
										formControlName="cpfpai"
										type="text"
										id="add-cliente-cadastro-cpfpai"
										[mask]="'000.000.000-00'" />
									<ds3-helper-message
										*ngIf="
											form.controls['cpfpai'].touched &&
											form.controls['cpfpai'].hasError('required')
										">
										É obrigatório preencher este campo
									</ds3-helper-message>
									<ds3-helper-message
										*ngIf="
											form.controls['cpfpai'].dirty &&
											form.controls['cpfpai'].hasError('mask')
										">
										cpf inválido
									</ds3-helper-message>
								</ds3-form-field>
							</ng-container>

							<ng-container *ngSwitchCase="'telefoneemergencia'">
								<ds3-form-field class="col-6">
									<ds3-field-label>
										Telefone Emergência
										<span *ngIf="campo.obrigatorio">*</span>
										<span *ngIf="campo.pendente">**</span>
									</ds3-field-label>
									<input
										ds3Input
										formControlName="telefoneemergencia"
										type="text"
										id="add-cliente-cadastro-telefoneemergencia"
										[mask]="'(00) 00000-0000'" />
									<ds3-helper-message
										*ngIf="
											form.controls['telefoneemergencia'].touched &&
											form.controls['telefoneemergencia'].hasError('required')
										">
										É obrigatório preencher este campo
									</ds3-helper-message>
									<ds3-helper-message
										*ngIf="
											form.controls['telefoneemergencia'].dirty &&
											form.controls['telefoneemergencia'].hasError('mask')
										">
										telefone inválido
									</ds3-helper-message>
								</ds3-form-field>
							</ng-container>

							<ng-container *ngSwitchDefault>
								<ds3-form-field
									class="col-6"
									*ngIf="campo | deveExibirCampo : isEstrangeiro">
									<ds3-field-label>
										{{ campo.label }}
										<span *ngIf="campo.obrigatorio">*</span>
										<span *ngIf="campo.pendente">**</span>
									</ds3-field-label>
									<input
										ds3Input
										id="add-cliente-cadastro-{{ campo._nome }}"
										[maxlength]="campo.maxlength"
										[formControlName]="campo._nome"
										type="text" />
									<ds3-helper-message
										*ngIf="
											form.controls[campo._nome].touched &&
											form.controls[campo._nome].hasError('required')
										">
										É obrigatório preencher este campo
									</ds3-helper-message>
									<ds3-helper-message
										*ngIf="
											form.controls[campo._nome].touched &&
											form.controls[campo._nome].hasError('mask')
										">
										Campo inválido, deve seguir o exemplo
										{{ campo.mask | json }}
									</ds3-helper-message>
								</ds3-form-field>
							</ng-container>
						</ng-container>

						<div
							*ngIf="index === 0"
							class="col-6 d-flex flex-row-reverse div-btn-superior">
							<button
								id="btn-sup-cadastro-prosseguir"
								class="flex-container ml-2"
								(click)="avancar()"
								[disabled]="!form.valid"
								ds3-flat-button>
								Prosseguir
								<i class="pct pct-chevron-right ml-2"></i>
							</button>
							<button
								id="btn-sup-cadastro-cancelar"
								class="flex-container"
								(click)="voltar()"
								ds3-outlined-button>
								Cancelar
							</button>
						</div>
					</div>
				</div>
				<div class="d-flex flex-row-reverse">
					<button
						id="btn-inf-cadastro-prosseguir"
						class="flex-container ml-2"
						(click)="avancar()"
						[disabled]="!form.valid"
						ds3-flat-button>
						Prosseguir
						<i class="pct pct-chevron-right ml-2"></i>
					</button>
					<button
						class="flex-container"
						id="btn-inf-cadastro-cancelar"
						(click)="voltar()"
						ds3-outlined-button>
						Cancelar
					</button>
				</div>
			</form>
		</ng-container>
	</pacto-cat-card-plain>
</adm-layout>
