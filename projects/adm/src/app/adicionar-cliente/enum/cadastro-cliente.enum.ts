export enum CampoLabelEnum {
	nome = "Nome completo",
	enderecocomplemento = "Complemento",
	cpfmae = "CPF da mãe ou responsável",
	cpfpai = "CPF do pai ou responsável",
	nomepaiouresponsavel = "Nome do pai ou responsável",
	nomemaeouresponsavel = "Nome da mãe ou responsável",
	cpf = "CPF",
	passaporte = "Passaporte",
	rne = "RNE",
	nomerespfinanceiro = "Nome do responsável financeiro",
	cpfrespfinanceiro = "CPF do responsável financeiro",
	rgrespfinanceiro = "RG do responsável financeiro",
	emailrespfinanceiro = "E-mail do responsável financeiro",
	telefone = "Tel. Celular",
	email = "E-mail",
	endereco = "Endereço",
	bairro = "Bairro",
	numero = "Número",
	pais = "País",
	estado = "Estado",
	cidade = "Cidade",
	datanascimento = "Data de Nascimento",
	sexo = "Sexo",
	contatoemergencia = "Contato de emergência",
	webpage = "Web page",
	nomeregistro = "Nome de Registro",
	rg = "RG",
	rgorgao = "Orgão Expedidor",
	rguf = "UF",
	rgmae = "RG da mãe",
	rgpai = "RG do pai",
	telefoneemergencia = "Telefone Emergência",
}

export enum typesMask {
	nome = "S* {15}",
	cpf = "000.000.000-00",
	rg = "0*.0*.0*-0*",
	telefone = "(00) 00000-0000",
	site = "A*",
	generico = "A* {15}",
	numero = "0*",
	email = "A* {50}",
}
export enum CampoMaskEnum {
	nomeregistro = typesMask.nome,
	cpf = typesMask.cpf,
	nomemaeouresponsavel = typesMask.nome,
	rgmae = typesMask.rg,
	cpfmae = typesMask.cpf,
	cpfpai = typesMask.cpf,
	rgpai = typesMask.rg,
	nomepaiouresponsavel = typesMask.nome,
	contatoemergencia = typesMask.nome,
	webpage = typesMask.site,
	endereco = typesMask.generico,
	bairro = typesMask.generico,
	complemento = typesMask.generico,
	numero = typesMask.numero,
	enderecocomplemento = typesMask.generico,
	cpfrespfinanceiro = typesMask.cpf,
	rgrespfinanceiro = typesMask.rg,
	emailrespfinanceiro = typesMask.email,
}

export enum CampoMaxLengthEnum {
	rg = "20",
	rgorgao = "10",
	rgmae = "20",
	rgpai = "20",
	nomepaiouresponsavel = "50",
	nomemaeouresponsavel = "50",
	nomeregistro = "80",
	contatoemergencia = "30",
	bairro = "35",
	enderecocomplemento = "40",
	numero = "10",
	endereco = "40",
	webpage = "50",
}

export enum CampoOrdem {
	nome,
	nomeregistro,
	cpf,
	rne,
	passaporte,
	rg,
	sexo,
	genero,
	datanascimento,
	categoria,
	profissao,
	graudeinstrucao,
	estadocivil,
	nomemaeouresponsavel,
	cpfmae,
	rgmae,
	nomepaiouresponsavel,
	cpfpai,
	rgpai,
	checkcadastroresponsavel,
	nomerespfinanceiro,
	cpfrespfinanceiro,
	rgrespfinanceiro,
	emailrespfinanceiro,
	telefone,
	email,
	contatoemergencia,
	telefoneemergencia,
	webpage,
	cep,
	endereco,
	bairro,
	enderecocomplemento,
	numero,
	pais,
	estado,
	cidade,
}
