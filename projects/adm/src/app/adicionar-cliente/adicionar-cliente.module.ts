import {
	CUSTOM_ELEMENTS_SCHEMA,
	NgModule,
	NO_ERRORS_SCHEMA,
} from "@angular/core";
import {
	CommonModule,
	DatePipe,
	DecimalPipe,
	DeprecatedI18NPipesModule,
} from "@angular/common";

import { AdicionarClienteRoutingModule } from "./adicionar-cliente-routing.module";
import { AddClienteListaComponent } from "./add-cliente-lista/add-cliente-lista.component";
import { AddClienteCadastroComponent } from "./add-cliente-cadastro/add-cliente-cadastro.component";
import { AddClienteBoletimComponent } from "./add-cliente-boletim/add-cliente-boletim.component";
import { AdicionarClienteComponent } from "@adm/adicionar-cliente/adicionar-cliente.component";
import { LayoutModule } from "@adm/layout/layout.module";
import { NgxMaskModule } from "ngx-mask";
import { CatTolltipModule, UiModule } from "ui-kit";
import { NgxCurrencyModule } from "ngx-currency";
import { BaseSharedModule } from "@base-shared/base-shared.module";
import { SdkModule } from "sdk";
import { DeveExibirCampoPipe } from "@adm/adicionar-cliente/deve-exibir-campo.pipe";
import { NgbModule } from "@ng-bootstrap/ng-bootstrap";
import { ModalAddProfissaoComponent } from "./modal-add-profissao/modal-add-profissao.component";
import { ModalAddFotoComponent } from "@adm/adicionar-cliente/modal-add-foto/modal-add-foto.component";
import { ModalTransferirClienteEmpresaComponent } from "./modal-transferir-cliente-empresa/modal-transferir-cliente-empresa.component";
import { ModalClienteSemelhanteComponent } from "./modal-cliente-semelhante/modal-cliente-semelhante.component";
import { ModalAddConfirmacaoComponent } from "./modal-add-confirmacao/modal-add-confirmacao.component";
import { ModalConsultaEnderecoComponent } from './modal-consulta-endereco/modal-consulta-endereco.component';

@NgModule({
	declarations: [
		AdicionarClienteComponent,
		AddClienteListaComponent,
		AddClienteCadastroComponent,
		AddClienteBoletimComponent,
		DeveExibirCampoPipe,
		ModalAddProfissaoComponent,
		ModalAddFotoComponent,
		ModalTransferirClienteEmpresaComponent,
		ModalClienteSemelhanteComponent,
		ModalAddConfirmacaoComponent,
		ModalConsultaEnderecoComponent,
	],
	entryComponents: [
		ModalAddProfissaoComponent,
		ModalAddFotoComponent,
		ModalTransferirClienteEmpresaComponent,
		ModalClienteSemelhanteComponent,
		ModalAddConfirmacaoComponent,
		ModalConsultaEnderecoComponent
	],
	imports: [
		CommonModule,
		UiModule,
		NgbModule,
		LayoutModule,
		SdkModule,
		CatTolltipModule,
		NgxCurrencyModule,
		BaseSharedModule,
		AdicionarClienteRoutingModule,
		NgxMaskModule,
	],
	schemas: [CUSTOM_ELEMENTS_SCHEMA, NO_ERRORS_SCHEMA],
	providers: [DecimalPipe, DatePipe],
})
export class AdicionarClienteModule {}
