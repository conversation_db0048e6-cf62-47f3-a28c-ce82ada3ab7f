import { Pipe, PipeTransform } from "@angular/core";

@Pipe({
	name: "deveExibirCampo",
})
export class DeveExibirCampoPipe implements PipeTransform {
	transform(campo: any, isEstrangeiro: boolean): boolean {
		const camposOcultos = [
			"estrangeiro",
			"cnpjsesiindustria",
			"matricula",
			"descricaotelefone",
		];

		if (
			isEstrangeiro &&
			(campo._nome === "rne" || campo._nome === "passaporte")
		) {
			return false;
		}

		if (camposOcultos.includes(campo._nome)) {
			return false;
		}

		return true;
	}
}
