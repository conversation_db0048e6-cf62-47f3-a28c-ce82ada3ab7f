import { ChangeDetectorRef, Component, Input, OnInit } from "@angular/core";
import { FormBuilder, FormGroup, Validators } from "@angular/forms";
import { Router } from "@angular/router";
import { ClientDiscoveryService, SessionService } from "sdk";
import { Observable } from "rxjs";
import { LayoutNavigationService, PlataformModuleConfig } from "pacto-layout";
import { AdicionarClienteService } from "@adm/adicionar-cliente/adicionar-cliente.service";
import { SnotifyService } from "ng-snotify";
import { PermissaoService } from "pacto-layout";
import { PactoModalSize } from "ui-kit";
import { ModalTransferirClienteEmpresaComponent } from "@adm/adicionar-cliente/modal-transferir-cliente-empresa/modal-transferir-cliente-empresa.component";
import { ModalService } from "@base-core/modal/modal.service";
import { AdmCoreApiNegociacaoService } from "adm-core-api";

@Component({
	selector: "adm-adicionar-cliente",
	templateUrl: "./adicionar-cliente.component.html",
	styleUrls: ["./adicionar-cliente.component.scss"],
})
export class AdicionarClienteComponent implements OnInit {
	formValuesCadastro: any;
	form: FormGroup;
	textInfo =
		"Antes de realizar o cadastro de um novo cliente, verifique se as " +
		"informações fornecidas já estão cadastradas no sistema. " +
		"Esse procedimento ajuda a evitar duplicações e mantém a integridade dos dados.";
	textInfo2 = "Essa consulta é realizada por qualquer parte do texto.";
	textoRestricao = "";
	clientes = [];
	dataCadastro: any;
	sortedColumn: string = "";
	sortOrder: "asc" | "desc" | "" = "";
	columns = [
		{ label: "Nome", key: "nome" },
		{ label: "CPF", key: "cpf" },
		{ label: "Telefone", key: "telefone" },
		{ label: "Empresa", key: "empresa" },
		{ label: "E-mail", key: "email" },
		{ label: "Origem", key: "origem" },
		{ label: "Situação", key: "situacao" },
	];
	recurso = "INCLUIR_CLIENTE";
	consultou = false;

	constructor(
		private router: Router,
		private fb: FormBuilder,
		private readonly snotifyService: SnotifyService,
		public sessionService: SessionService,
		private cd: ChangeDetectorRef,
		private pactoModal: ModalService,
		private permissaoService: PermissaoService,
		private navigationService: LayoutNavigationService,
		private admCoreApiNegociacaoService: AdmCoreApiNegociacaoService,
		private clientDiscoveryService: ClientDiscoveryService,
		private readonly adicionarClienteService: AdicionarClienteService
	) {}

	ngOnInit() {
		this.buildForm();
		this.adicionarClienteService.empresaSelecionada = Number(
			this.sessionService.empresaId
		);
		this.adicionarClienteService.boletim = null;

		if (this.adicionarClienteService.filtro) {
			if (this.adicionarClienteService.filtro.cpf) {
				this.form.get("cpf").setValue(this.adicionarClienteService.filtro.cpf);
			}
			if (this.adicionarClienteService.filtro.nome) {
				this.form
					.get("nome")
					.setValue(this.adicionarClienteService.filtro.nome);
			}
			if (this.adicionarClienteService.filtro.email) {
				this.form
					.get("email")
					.setValue(this.adicionarClienteService.filtro.email);
			}
			if (this.adicionarClienteService.filtro.telefone) {
				this.form
					.get("telefone")
					.setValue(this.adicionarClienteService.filtro.telefone);
			}
		}
		this.notificar("INCLUIR_CLIENTE_V2_ENTROU");
	}

	private buildForm() {
		this.form = this.fb.group(
			{
				cpf: [null],
				nome: [null],
				email: [null, Validators.email],
				telefone: [null],
			},
			Validators.nullValidator
		);
	}

	private showWarning(message: string) {
		this.form.markAllAsTouched();
		this.snotifyService.warning(message);
	}

	consultar() {
		this.formValuesCadastro = this.filterFormValues(this.form.getRawValue());
		const cpf = this.form.get("cpf").value;
		const nome = this.form.get("nome").value;
		const email = this.form.get("email").value;
		const telefone = this.form.get("telefone").value;
		this.clientes = [];

		if (!email && !cpf && !nome && !telefone) {
			return this.showWarning(
				"Preencha pelo menos um dos campos: CPF, nome, telefone ou e-mail."
			);
		}

		this.consultarRestricao(cpf);

		this.adicionarClienteService.filtro = {
			nome,
			cpf,
			email,
			telefone,
		};

		this.adicionarClienteService
			.consultar(
				Number(this.sessionService.empresaId),
				nome,
				cpf,
				email,
				telefone
			)
			.subscribe(
				(resp) => {
					this.clientes = resp;
					this.consultou = true;
					if (this.clientes.length === 0) {
						this.snotifyService.info(
							"Nenhum cliente encontrado com esses dados"
						);
					}
					this.cd.detectChanges();
				},
				({ error = {} }) => {
					const message =
						error.meta && error.meta.message
							? error.meta.message
							: "Erro ao consultar clientes";
					this.snotifyService.error(message);
				}
			);
		this.cd.detectChanges();
	}

	consultarRestricao(cpf: string): void {
		this.textoRestricao = "";
		const cpfNum = String(cpf ? cpf : '').replace(/\D/g, '');
		if (!cpfNum || cpfNum.length !== 11) {
			// CPF inválido ou incompleto
			return;
		}
		this.adicionarClienteService
			.consultarRestricao(
				Number(this.sessionService.empresaId),
				'',
				cpfNum,
				'',
				'',
			)
			.subscribe(
				(resp) => {
					this.textoRestricao = resp;
					this.cd.detectChanges();
				},
				({ error = {} }) => {
					const message =
						error.meta && error.meta.message
							? error.meta.message
							: "Erro ao consultar restrição";
					this.snotifyService.error(message);
					this.cd.detectChanges();
				},
			);
	}

	private filterFormValues(formValue: any): any {
		return Object.keys(formValue)
			.filter((key) => formValue[key] !== null && formValue[key] !== "")
			.reduce((obj, key) => {
				obj[key] = formValue[key];
				return obj;
			}, {});
	}

	permitirSomenteTexto(event: KeyboardEvent): void {
		const charCode = event.charCode || event.keyCode;
		if (
			!(
				(charCode >= 65 && charCode <= 90) ||
				(charCode >= 97 && charCode <= 122) ||
				charCode === 32 ||
				charCode >= 192
			)
		) {
			event.preventDefault();
		}
	}

	getDataCadastro(data: any) {
		this.dataCadastro = data;
	}

	updateDataForm(data: any) {
		this.formValuesCadastro = data;
	}

	voltarHome(): void {
		(
			this.navigationService.redirectToModule(
				PlataformModuleConfig.PERSONS_LEGADO
			) as Observable<any>
		).subscribe((data) => {
			window.open(data.url, "_self");
		});
	}

	acessarVersaoAntiga(event) {
		this.clientDiscoveryService
			.linkZw(this.sessionService.usuarioOamd, this.sessionService.empresaId)
			.subscribe((urlZw) => {
				let url = `${urlZw}&jspPage=preCadastro.jsp&menu=true&origem=angular`;
				window.open(url, "_self");
			});
	}

	sortTable(column: any): void {
		const key = column.key;
		if (this.sortedColumn === key) {
			this.sortOrder = this.sortOrder === "asc" ? "desc" : "asc";
		} else {
			this.sortedColumn = key;
			this.sortOrder = "asc";
		}

		this.clientes.sort((a, b) => {
			const valA = a[key] || "";
			const valB = b[key] || "";

			let comparison = 0;

			if (typeof valA === "string" && typeof valB === "string") {
				comparison = valA.localeCompare(valB);
			} else {
				comparison = valA < valB ? -1 : valA > valB ? 1 : 0;
			}

			if (comparison === 0) {
				comparison = a["nome"].localeCompare(b["nome"]) || 0;
			}

			return this.sortOrder === "asc" ? comparison : -comparison;
		});
	}

	getSortIcon(column: any): string {
		if (this.sortedColumn === column.key) {
			return this.sortOrder === "asc" ? "pct-caret-up" : "pct-caret-down";
		}
		return "pct-drop-down";
	}

	adicionar() {
		const cpf = this.form.get("cpf").value;
		const nome = this.form.get("nome").value;
		const email = this.form.get("email").value;
		this.adicionarClienteService.clienteSelecionado = {
			empresa: JSON.stringify({
				codigo: Number(this.sessionService.empresaId),
			}),
			nome,
			cpf,
			email,
		};
		this.router.navigateByUrl("/adm/adicionar-cliente/cadastro");
	}

	selecionarCliente(cliente) {
		if (
			cliente.cliente &&
			cliente.cliente > 0 &&
			Number(this.sessionService.empresaId) !== cliente.empresa.codigo
		) {
			this.adicionarClienteService.validarTrocaEmpresa(cliente).subscribe(
				(respValida) => {
					const dialogRef = this.pactoModal.open(
						"Transferir cliente de empresa",
						ModalTransferirClienteEmpresaComponent,
						PactoModalSize.LARGE
					);
					dialogRef.componentInstance.dados = respValida;
					dialogRef.componentInstance.update.subscribe((respModal) => {
						const body = {
							...cliente,
							empresaTrocaEmpresa: Number(this.sessionService.empresaId),
							alterarConvenioCobrancaTrocaEmpresa:
								respModal.alterarConvenioCobrancaTrocaEmpresa,
							convenioCobrancaTrocaEmpresa: respModal.convenioCobranca,
						};

						this.adicionarClienteService.gravar(body).subscribe(
							(respGravar: any) => {
								this.acessarPerfilAluno(respGravar.matricula);
							},
							({ error = {} }) => {
								this.snotifyService.error(error.meta.message);
							}
						);
					});
				},
				({ error = {} }) => {
					this.snotifyService.error(error.meta.message);
				}
			);
		} else {
			if (cliente.matricula && cliente.matricula.length > 0) {
				this.acessarPerfilAluno(cliente.matricula);
			} else {
				this.adicionarClienteService.clienteSelecionado = cliente;
				this.router.navigateByUrl("/adm/adicionar-cliente/cadastro");
			}
		}
	}

	acessarPerfilAluno(matricula) {
		this.admCoreApiNegociacaoService.recursoHabilitado("TELA_ALUNO").subscribe({
			next: (responseV) => {
				if (responseV) {
					this.acessarPerfilAlunoNovo(matricula);
				} else {
					this.acessarPerfilAlunoAntigo(matricula);
				}
			},
			error: (err) => {
				this.acessarPerfilAlunoAntigo(matricula);
			},
		});
	}

	acessarPerfilAlunoNovo(matricula) {
		this.router.navigateByUrl("/pessoas/perfil-v2/" + matricula);
	}

	acessarPerfilAlunoAntigo(matricula) {
		this.clientDiscoveryService
			.linkZw(this.sessionService.usuarioOamd, this.sessionService.empresaId)
			.subscribe((urlZw) => {
				const url = `${urlZw}&urlRedirect=uriCliente&matriculaCliente=${matricula}&menu=true`;
				window.open(url, "_self");
			});
	}

	private notificar(string: string) {
		try {
			this.sessionService.notificarRecursoEmpresa(string);
		} catch (e) {
			console.error(e);
		}
	}
}
