import {
	ChangeDetectorRef,
	Component,
	EventEmitter,
	OnInit,
	Output,
} from "@angular/core";
import { NgbActiveModal } from "@ng-bootstrap/ng-bootstrap";
import { SessionService } from "@base-core/client/session.service";
import { SnotifyService } from "ng-snotify";
import { FormControl, FormGroup } from "@angular/forms";
import { CaptalizePipe } from "ui-kit";

@Component({
	selector: "adm-modal-transferir-cliente-empresa",
	templateUrl: "./modal-transferir-cliente-empresa.component.html",
	styleUrls: ["./modal-transferir-cliente-empresa.component.scss"],
})
export class ModalTransferirClienteEmpresaComponent implements OnInit {
	dados: any;
	form: FormGroup;
	@Output() update = new EventEmitter<any>();
	convenios: Array<any> = new Array();
	private captalizePipe = new CaptalizePipe();

	constructor(
		private modal: NgbActiveModal,
		private sessionService: SessionService,
		private snotifyService: SnotifyService,
		private cd: ChangeDetectorRef
	) {}

	ngOnInit() {
		this.form = new FormGroup({
			convenioCobranca: new FormControl(),
		});

		if (
			this.dados.listaConvenioCobrancaTrocaEmpresa &&
			this.dados.listaConvenioCobrancaTrocaEmpresa.length > 0
		) {
			this.convenios.push({
				id: "NAO_ALTERAR",
				label: "Não alterar o convênio de cobrança",
			});

			this.dados.listaConvenioCobrancaTrocaEmpresa.forEach((item) => {
				this.convenios.push({
					id: item.codigo.toString(),
					label: this.captalizePipe.transform(item.descricao),
				});
			});
		}
	}

	close(): void {
		this.modal.dismiss();
	}

	salvar(): void {
		const convenio = this.form.get("convenioCobranca").value;

		if (
			this.dados.listaConvenioCobrancaTrocaEmpresa &&
			this.dados.listaConvenioCobrancaTrocaEmpresa.length > 0 &&
			!convenio
		) {
			this.snotifyService.error("Selecione um opção do convênio de cobrança");
			return;
		}

		let convenioEnviar = convenio ? convenio.id : 0;
		let alterarConvenioCobrancaTrocaEmpresa = true;
		if (convenioEnviar === "NAO_ALTERAR") {
			alterarConvenioCobrancaTrocaEmpresa = false;
			convenioEnviar = 0;
		}

		let resp = {
			alterarConvenioCobrancaTrocaEmpresa,
			convenioCobranca: Number(convenioEnviar),
		};
		this.update.emit(resp);
		this.close();
	}
}
