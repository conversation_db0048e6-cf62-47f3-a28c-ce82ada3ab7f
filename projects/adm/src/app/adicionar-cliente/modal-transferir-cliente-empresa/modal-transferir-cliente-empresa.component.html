<div class="container">
	<div class="div-superior">
		<div>
			<span class="msg-principal">
				Este cliente é de outra unidade. Clique em "Transferir cliente para esta
				empresa" para trazê-lo para sua unidade.
			</span>
		</div>
		<div>
			<span class="msg-principal">
				Ao transferir este aluno, o contrato será cancelado na empresa de origem
				e um novo contrato com a mesma vigência final será gerado na empresa de
				destino. Sendo assim, todas as parcelas em aberto do cliente serão
				transferidas para empresa de destino.
			</span>
		</div>
		<div>
			<span class="msg-alerta">{{ dados?.msgAdicionalTrocaEmpresa }}</span>
		</div>
		<div>
			<span class="msg-alerta">
				{{ dados?.msgTrocarConvenioCobrancaTrocaEmpresa }}
			</span>
		</div>
		<div *ngIf="dados?.apresentarTrocaConvenioCobrancaTrocaEmpresa">
			<div>
				<pacto-cat-form-select-filter
					id="convenio-transferir"
					[control]="form.get('convenioCobranca')"
					[options]="convenios"
					[addEmptyOption]="false"
					label="Convênio de cobrança"
					idKey="id"
					labelKey="label"></pacto-cat-form-select-filter>
			</div>
		</div>
	</div>
	<div class="d-flex justify-content-center gap-1">
		<pacto-cat-button
			(click)="salvar()"
			id="btn-mdl-transferir-cliente-empresa-transferir"
			label="Transferir cliente para esta empresa"
			size="MEDIUM"></pacto-cat-button>

		<pacto-cat-button
			(click)="close()"
			id="btn-mdl-transferir-cliente-empresa-cancelar"
			label="Cancelar"
			size="MEDIUM"
			type="OUTLINE"></pacto-cat-button>
	</div>
</div>
