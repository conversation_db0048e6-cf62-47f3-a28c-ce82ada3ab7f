import { CommonModule } from "@angular/common";
import { NgModule } from "@angular/core";
import { ReactiveFormsModule } from "@angular/forms";
import { UiModule } from "ui-kit";
import { LayoutModule } from "../layout/layout.module";

import { AutorizacaoAcessoRoutingModule } from "./autorizacao-acesso-routing.module";
import { AutorizacaoAcessoFormComponent } from "./components/autorizacao-acesso-form/autorizacao-acesso-form.component";
import { AutorizacaoAcessoListaComponent } from "./components/autorizacao-acesso-lista/autorizacao-acesso-lista.component";
import { AutorizacaoAcessoPasswordModalComponent } from "./components/autorizacao-acesso-password-modal/autorizacao-acesso-password-modal.component";

@NgModule({
	declarations: [
		AutorizacaoAcessoListaComponent,
		AutorizacaoAcessoFormComponent,
		AutorizacaoAcessoPasswordModalComponent,
	],
	entryComponents: [AutorizacaoAcessoPasswordModalComponent],
	imports: [
		CommonModule,
		LayoutModule,
		ReactiveFormsModule,
		UiModule,
		AutorizacaoAcessoRoutingModule,
	],
})
export class AutorizacaoAcessoModule {}
