import { async, ComponentFixture, TestBed } from "@angular/core/testing";

import { AutorizacaoAcessoPasswordModalComponent } from "./autorizacao-acesso-password-modal.component";

describe("AutorizacaoAcessoPasswordModalComponent", () => {
	let component: AutorizacaoAcessoPasswordModalComponent;
	let fixture: ComponentFixture<AutorizacaoAcessoPasswordModalComponent>;

	beforeEach(async(() => {
		TestBed.configureTestingModule({
			declarations: [AutorizacaoAcessoPasswordModalComponent],
		}).compileComponents();
	}));

	beforeEach(() => {
		fixture = TestBed.createComponent(AutorizacaoAcessoPasswordModalComponent);
		component = fixture.componentInstance;
		fixture.detectChanges();
	});

	it("should create", () => {
		expect(component).toBeTruthy();
	});
});
