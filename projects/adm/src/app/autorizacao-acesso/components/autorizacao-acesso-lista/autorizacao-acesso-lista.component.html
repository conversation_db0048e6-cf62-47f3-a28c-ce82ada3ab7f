<adm-layout
	appStickyFooter
	i18n-pageTitle="@@autorizacao-acesso:title"
	modulo="Administrativo"
	pageTitle="Autorização de acesso">
	<pacto-relatorio
		(btnAddClick)="navigateToNovaAutorizacaoAcesso()"
		[actionTitulo]="traducao.getLabel('table-column-acoes')"
		[enableDs3]="true"
		[filterConfig]="filterConfig"
		[showBtnAdd]="true"
		[table]="tableAutorizacaoAcesso"
		i18n-labelBtnAdd="@@label-add-btn"
		labelBtnAdd="Cadastrar nova autorização de acesso"
		quickSearchPlaceHolderCustom="Busque pelo nome do aluno ou matrícula"></pacto-relatorio>
</adm-layout>

<pacto-traducoes-xingling #traducao>
	<span i18n="@@autorizacao-acesso:edit" xingling="btn-edit">
		Editar autorização
	</span>
	<span i18n="@@autorizacao-acesso:delete" xingling="btn-delete">
		Excluir autorização
	</span>
	<span i18n="@@adm:action-editar" xingling="action-editar">Editar</span>
	<span i18n="@@adm:action-excluir" xingling="action-excluir">Excluir</span>
	<span
		i18n="@@cad-aux:cidade:table-column-acoes"
		xingling="table-column-acoes">
		Ações
	</span>
</pacto-traducoes-xingling>

<ng-template #nameCell>
	<button ds3-text-button size="sm">
		<ds3-avatar [size]="24"></ds3-avatar>
		Nome do cliente
	</button>
</ng-template>
<ng-template #passwordCell>
	<button
		(click)="openAutorizacaoAcessoPasswordModal()"
		ds3-text-button
		size="sm">
		Alterar senha
	</button>
</ng-template>
