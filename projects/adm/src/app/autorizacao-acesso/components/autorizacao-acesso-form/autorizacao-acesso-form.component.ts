import { Component, OnInit } from "@angular/core";
import { FormControl, FormGroup, Validators } from "@angular/forms";
import { ActivatedRoute, Router } from "@angular/router";
import { DialogService, PactoModalSize } from "ui-kit";
import { AutorizacaoAcessoPasswordModalComponent } from "../autorizacao-acesso-password-modal/autorizacao-acesso-password-modal.component";

@Component({
	selector: "adm-autorizacao-acesso-form",
	templateUrl: "./autorizacao-acesso-form.component.html",
	styleUrls: ["./autorizacao-acesso-form.component.scss"],
})
export class AutorizacaoAcessoFormComponent implements OnInit {
	isEditing = false;
	formGroup: FormGroup;
	optionsAlunos;
	optionsIntegracao;
	optionsEmpresa;
	id: string;
	isPasswordEmpty: boolean;

	constructor(
		private router: Router,
		private dialogService: DialogService,
		private activatedRoute: ActivatedRoute
	) {}

	ngOnInit() {
		this.setId();
		this.setIsEditing();
		this.createForm();
		this.loadAlunos();
		this.loadEmpresas();
		this.loadIntegracoes();
		this.setupFormTipoChanges();

		if (this.isEditing) {
			this.loadAutorizacaoAcesso();
		}
	}

	setId() {
		this.id = this.activatedRoute.snapshot.paramMap.get("id");
	}

	setIsEditing() {
		if (this.id) {
			this.isEditing = true;
		}
	}

	createForm() {
		this.formGroup = new FormGroup({
			codAutorizacao: new FormControl({ value: "040020", disabled: true }),
			integracao: new FormControl(null, [Validators.required]),
			empresaRemota: new FormControl(null, [Validators.required]),
			tipoAluno: new FormControl(true),
			tipoColaborador: new FormControl(false),
			tipoAmbos: new FormControl(false),
			aluno: new FormControl(null, [Validators.required]),
			matricula: new FormControl({ value: "45248202", disabled: true }),
			codAcesso: new FormControl({ value: "30183018", disabled: true }),
			codAcessoAlternativo: new FormControl({
				value: "040020",
				disabled: true,
			}),
			dataNascimento: new FormControl({ value: "00/00/0000", disabled: true }),
			email: new FormControl({
				value: "<EMAIL>",
				disabled: true,
			}),
			telefone: new FormControl({ value: "(00) 0000-0000", disabled: true }),
		});
	}

	loadEmpresas() {
		this.optionsEmpresa = [
			{
				value: "empresa",
				name: "Empresa",
			},
		];
	}

	loadIntegracoes() {
		this.optionsIntegracao = [
			{
				value: null,
				name: "-",
			},
			{
				value: "int",
				name: "Integração",
			},
		];
	}

	loadAlunos() {
		this.optionsAlunos = [
			{
				value: "aluno",
				name: "Aluno",
			},
		];
	}

	loadAutorizacaoAcesso() {
		this.formGroup.controls["aluno"].setValue("aluno");
		this.formGroup.controls["integracao"].setValue("int");
		this.formGroup.controls["empresaRemota"].setValue("empresa");
	}

	setIsPasswordEmpty(password) {
		this.isPasswordEmpty = true;
	}

	openAutorizacaoAcessoPasswordModal() {
		const dialogRef = this.dialogService.open(
			"Cadastrar senha de integração",
			AutorizacaoAcessoPasswordModalComponent,
			PactoModalSize.LARGE
		);
	}

	save() {
		if (this.formGroup.invalid) {
			this.formGroup.markAllAsTouched();
			return;
		}

		console.log(this.formGroup.value);
	}

	voltarParaLista() {
		this.router.navigate(["adm/autorizacao-acesso"]);
	}

	private setupFormTipoChanges(): void {
		const tipoAlunoControl = this.formGroup.get("tipoAluno");
		const tipoColaboradorControl = this.formGroup.get("tipoColaborador");
		const tipoAmbosControl = this.formGroup.get("tipoAmbos");

		tipoAlunoControl.valueChanges.subscribe(() => {
			this.updateTipoAmbos();
		});

		tipoColaboradorControl.valueChanges.subscribe(() => {
			this.updateTipoAmbos();
		});

		tipoAmbosControl.valueChanges.subscribe((value) => {
			if (!value) {
				tipoColaboradorControl.setValue(false, { emitEvent: false });
				return;
			}

			tipoAlunoControl.setValue(true, { emitEvent: false });
			tipoColaboradorControl.setValue(true, { emitEvent: false });
		});
	}

	private updateTipoAmbos(): void {
		const tipoAlunoValue = this.formGroup.get("tipoAluno").value;
		const tipoColaboradorValue = this.formGroup.get("tipoColaborador").value;
		const tipoAmbosControl = this.formGroup.get("tipoAmbos");

		if (tipoAlunoValue && tipoColaboradorValue) {
			tipoAmbosControl.setValue(true, { emitEvent: false });
		} else {
			tipoAmbosControl.setValue(false, { emitEvent: false });
		}
	}
}
