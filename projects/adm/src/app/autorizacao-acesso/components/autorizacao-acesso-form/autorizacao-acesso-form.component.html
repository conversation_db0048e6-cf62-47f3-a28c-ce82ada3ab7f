<adm-layout
	(goBack)="voltarParaLista()"
	[pageTitle]="
		isEditing
			? 'Editar autorização de acesso'
			: 'Cadastrar autorização de acesso'
	"
	appStickyFooter
	i18n-pageTitle="@@autorizacao-acesso:title"
	modulo="Autorização de acesso">
	<div [class.isEditing]="isEditing" class="autorizacao-acesso-form">
		<div class="row autorizacao-acesso-form-info">
			<div
				*ngIf="isEditing"
				[ngStyle]="isEditing && { order: '1' }"
				class="col-2">
				<ds3-form-field>
					<ds3-field-label>Cód. da autorização</ds3-field-label>
					<input [formControl]="formGroup.get('codAutorizacao')" ds3Input />
				</ds3-form-field>
			</div>
			<div
				[ngClass]="isEditing ? 'col-4' : 'col-6'"
				[ngStyle]="isEditing && { order: '2' }">
				<ds3-form-field>
					<ds3-field-label>Integração*</ds3-field-label>
					<ds3-select
						[formControl]="formGroup.get('integracao')"
						[options]="optionsIntegracao"
						ds3Input></ds3-select>
				</ds3-form-field>
			</div>
			<div
				[ngStyle]="isEditing && { order: '5' }"
				class="col-6 autorizacao-acesso-form-empresa">
				<ds3-form-field>
					<ds3-field-label>Empresa remota*</ds3-field-label>
					<ds3-select
						[formControl]="formGroup.get('empresaRemota')"
						[options]="optionsEmpresa"
						ds3Input></ds3-select>
				</ds3-form-field>
			</div>
			<div [ngStyle]="isEditing && { order: '3' }" class="col-6">
				<ds3-form-field class="autorizacao-acesso-form-tipo">
					<ds3-field-label>Tipo*</ds3-field-label>
					<ds3-checkbox [formControl]="formGroup.get('tipoAluno')" ds3Input>
						Aluno
					</ds3-checkbox>
					<ds3-checkbox
						[formControl]="formGroup.get('tipoColaborador')"
						ds3Input>
						Colaborador
					</ds3-checkbox>
					<ds3-checkbox [formControl]="formGroup.get('tipoAmbos')" ds3Input>
						Ambos
					</ds3-checkbox>
				</ds3-form-field>
				<ds3-diviser></ds3-diviser>
			</div>
			<div [ngStyle]="isEditing && { order: '4' }" class="col-6">
				<ds3-form-field>
					<ds3-field-label>Selecionar autorizado*</ds3-field-label>
					<ds3-select
						[formControl]="formGroup.get('aluno')"
						[options]="optionsAlunos"
						ds3Input></ds3-select>
				</ds3-form-field>
			</div>
		</div>
		<div class="row">
			<ng-container *ngIf="formGroup.get('aluno').value">
				<div class="col-3">
					<ds3-form-field>
						<ds3-field-label>Matrícula</ds3-field-label>
						<input
							[formControl]="formGroup.get('matricula')"
							ds3Input
							type="text" />
					</ds3-form-field>
				</div>
				<div class="col-3">
					<ds3-form-field>
						<ds3-field-label>Cód. Acesso</ds3-field-label>
						<input
							[formControl]="formGroup.get('codAcesso')"
							ds3Input
							type="text" />
					</ds3-form-field>
				</div>
				<div class="col-3">
					<ds3-form-field>
						<ds3-field-label>Cód. Acesso Alternativo</ds3-field-label>
						<input
							[formControl]="formGroup.get('codAcessoAlternativo')"
							ds3Input
							type="text" />
					</ds3-form-field>
				</div>
				<div class="col-3">
					<ds3-form-field>
						<ds3-field-label>Data de nascimento</ds3-field-label>
						<input
							[formControl]="formGroup.get('dataNascimento')"
							ds3Input
							type="text" />
					</ds3-form-field>
				</div>

				<div class="col-6">
					<ds3-form-field>
						<ds3-field-label>Telefone</ds3-field-label>
						<input
							[formControl]="formGroup.get('telefone')"
							ds3Input
							type="text" />
					</ds3-form-field>
				</div>
				<div class="col-6">
					<ds3-form-field>
						<ds3-field-label>E-mail</ds3-field-label>
						<input
							[formControl]="formGroup.get('email')"
							ds3Input
							type="email" />
					</ds3-form-field>
				</div>
			</ng-container>

			<div class="col-12">
				<div class="autorizacao-acesso-form-footer">
					<ng-container *ngIf="!isEditing">
						<button (click)="save()" ds3-flat-button>
							Salvar autorização de acesso
						</button>
					</ng-container>
					<ng-container *ngIf="isEditing">
						<button color="secondary" ds3-outlined-button>
							Excluir autorização de acesso
						</button>
						<button
							(click)="openAutorizacaoAcessoPasswordModal()"
							ds3-outlined-button>
							{{ isPasswordEmpty ? "Cadastrar" : "Alterar" }} senha de acesso
							integração
						</button>
						<button (click)="save()" ds3-flat-button>Salvar alterações</button>
					</ng-container>
				</div>
			</div>
		</div>
	</div>
</adm-layout>
