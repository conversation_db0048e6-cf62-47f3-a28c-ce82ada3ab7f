import {
	ChangeDetectorRef,
	Component,
	OnInit,
	TemplateRef,
	ViewChild,
} from "@angular/core";
import { Router } from "@angular/router";
import { CadastroAuxApiCategoriaService } from "cadastro-aux-api";
import { SnotifyService } from "ng-snotify";
import { PerfilAcessoRecurso, SessionService } from "sdk";
import {
	PactoDataGridConfig,
	RelatorioComponent,
	TraducoesXinglingComponent,
} from "ui-kit";
import { AdmRestService } from "../../../adm-rest.service";
import { PerfilAcessoRecursoNome } from "../../../perfil-acesso/perfil-acesso-recurso.model";
import { getTipoCategoriaLabelByKey } from "../../enums/TipoCategoria";

@Component({
	selector: "adm-categoria",
	templateUrl: "./categoria.component.html",
	styleUrls: ["./categoria.component.scss"],
})
export class CategoriaComponent implements OnInit {
	@ViewChild("tableData", { static: false }) tableData: RelatorioComponent;
	@ViewChild("traducao", { static: true }) traducao: TraducoesXinglingComponent;
	@ViewChild("columnCodigo", { static: true }) columnCodigo: TemplateRef<any>;
	@ViewChild("columnNome", { static: true }) columnNome: TemplateRef<any>;
	@ViewChild("columnTipoCategoria", { static: true })
	columnTipoCategoria: TemplateRef<any>;
	table: PactoDataGridConfig;
	recurso: PerfilAcessoRecurso;

	constructor(
		private router: Router,
		private cd: ChangeDetectorRef,
		private session: SessionService,
		private admRest: AdmRestService,
		private notificationService: SnotifyService,
		private categoriaService: CadastroAuxApiCategoriaService
	) {
		this.recurso = this.session.recursos.get(PerfilAcessoRecursoNome.CATEGORIA);
	}

	ngOnInit() {
		this.initTable();
	}

	editarCategoria(categoria) {
		this.router.navigate(["adm", "cad-aux", "categoria", categoria.codigo]);
	}

	deleteCategoria(categoria: any) {
		if (this.recurso.excluir) {
			this.categoriaService.delete(categoria.codigo).subscribe(
				(response) => {
					this.notificationService.success(
						this.traducao.getLabel("DELETE_MSG")
					);
					this.tableData.reloadData();
				},
				(httpErrorResponse) => {
					const err = httpErrorResponse.error;
					if (err.meta && err.meta.messageValue) {
						this.notificationService.error(err.meta.messageValue);
					}
				}
			);
		} else {
			this.notificationService.warning(
				this.traducao.getLabel("PERMISSION_MSG")
			);
		}
	}

	novoCategoria() {
		this.router.navigate(["adm", "cad-aux", "novo-categoria"]);
	}

	voltarHome() {
		this.router.navigate(["adm"]);
	}

	iconClickFn(event: { row: any; iconName: string }) {
		if (event.iconName === "action-edit (key)") {
			this.editarCategoria(event.row);
		} else if (event.iconName === "action-delete (key)") {
			this.deleteCategoria(event.row);
		} else {
			this.notificationService.warning(
				this.traducao.getLabel("msg-usuario-sem-permissao")
			);
		}
	}

	private initTable() {
		setTimeout(() => {
			this.table = new PactoDataGridConfig({
				endpointUrl: this.admRest.buildFullUrlCadAux("/categoria"),
				quickSearch: true,
				ghostLoad: true,
				ghostAmount: 5,
				showFilters: true,
				columns: [
					{
						nome: "codigo",
						titulo: this.columnCodigo,
						visible: true,
						ordenavel: true,
					},
					{
						nome: "nome",
						titulo: this.columnNome,
						visible: true,
						ordenavel: true,
					},
					{
						nome: "tipoCategoria",
						titulo: this.columnTipoCategoria,
						visible: true,
						ordenavel: true,
						valueTransform: (v) => getTipoCategoriaLabelByKey(v, this.traducao),
					},
				],
				actions: [
					{
						nome: this.traducao.getLabel("action-edit"),
						iconClass: "pct pct-edit cor-action-default-able04",
						tooltipText: this.traducao.getLabel("BTTN_EDIT"),
						actionFn: (row) => this.editarCategoria(row),
					},
					{
						nome: this.traducao.getLabel("action-delete"),
						iconClass: "pct pct-trash-2 cor-action-default-risk04",
						tooltipText: this.traducao.getLabel("BTTN_DELETE"),
						actionFn: (row) => this.editarCategoria(row),
					},
				],
			});
			this.cd.detectChanges();
		});
	}
}
