import {
	ChangeDetectorRef,
	Component,
	OnInit,
	TemplateRef,
	ViewChild,
} from "@angular/core";
import { Router } from "@angular/router";
import { CadastroAuxApiClassificacaoService } from "cadastro-aux-api";
import { SnotifyService } from "ng-snotify";
import { PerfilAcessoRecurso, SessionService } from "sdk";
import {
	PactoDataGridConfig,
	RelatorioComponent,
	TraducoesXinglingComponent,
} from "ui-kit";
import { AdmRestService } from "../../../adm-rest.service";
import { PerfilAcessoRecursoNome } from "../../../perfil-acesso/perfil-acesso-recurso.model";

@Component({
	selector: "adm-classificacao",
	templateUrl: "./classificacao.component.html",
	styleUrls: ["./classificacao.component.scss"],
})
export class ClassificacaoComponent implements OnInit {
	@ViewChild("tableData", { static: false }) tableData: RelatorioComponent;
	@ViewChild("traducoes", { static: true })
	traducao: TraducoesXinglingComponent;
	@ViewChild("columnCodigo", { static: true }) columnCodigo: TemplateRef<any>;
	@ViewChild("columnNome", { static: true }) columnNome: TemplateRef<any>;
	@ViewChild("columnSMSAuto", { static: true }) columnSMSAuto: TemplateRef<any>;
	table: PactoDataGridConfig;
	recurso: PerfilAcessoRecurso;

	constructor(
		private router: Router,
		private cd: ChangeDetectorRef,
		private session: SessionService,
		private admRest: AdmRestService,
		private notificationService: SnotifyService,
		private classificacaoService: CadastroAuxApiClassificacaoService
	) {
		this.recurso = this.session.recursos.get(
			PerfilAcessoRecursoNome.CLASSIFICACAO
		);
	}

	ngOnInit() {
		this.initTable();
	}

	editarClassificacao(classificacao) {
		this.router.navigate([
			"adm",
			"cad-aux",
			"classificacao",
			classificacao.codigo,
		]);
	}

	deleteClassificacao(classificacao: any) {
		if (this.recurso.excluir) {
			this.classificacaoService.delete(classificacao.codigo).subscribe(
				(response) => {
					this.notificationService.success(
						this.traducao.getLabel("DELETE_MSG")
					);
					this.tableData.reloadData();
				},
				(httpErrorResponse) => {
					const err = httpErrorResponse.error;
					if (err.meta && err.meta.messageValue) {
						this.notificationService.error(err.meta.messageValue);
					}
				}
			);
		} else {
			this.notificationService.warning(
				this.traducao.getLabel("PERMISSION_MSG")
			);
		}
	}

	novoClassificacao() {
		this.router.navigate(["adm", "cad-aux", "novo-classificacao"]);
	}

	voltarHome() {
		this.router.navigate(["adm"]);
	}

	iconClickFn(event: { row: any; iconName: string }) {
		if (event.iconName === "action-edit (key)") {
			this.editarClassificacao(event.row);
		} else if (event.iconName === "action-delete (key)") {
			this.deleteClassificacao(event.row);
		} else {
			this.notificationService.warning(
				this.traducao.getLabel("PERMISSION_MSG")
			);
		}
	}

	private initTable() {
		setTimeout(() => {
			this.table = new PactoDataGridConfig({
				endpointUrl: this.admRest.buildFullUrlCadAux("/classificacao"),
				quickSearch: true,
				ghostLoad: true,
				ghostAmount: 5,
				showFilters: true,
				columns: [
					{
						nome: "codigo",
						titulo: this.columnCodigo,
						visible: true,
						ordenavel: true,
					},
					{
						nome: "nome",
						titulo: this.columnNome,
						visible: true,
						ordenavel: true,
					},
					{
						nome: "enviarsmsautomatico",
						titulo: this.columnSMSAuto,
						visible: true,
						ordenavel: true,
						valueTransform: (v) =>
							v
								? this.traducao.getLabel("ADM_SIM")
								: this.traducao.getLabel("ADM_NAO"),
					},
				],
				actions: [
					{
						nome: this.traducao.getLabel("action-edit"),
						iconClass: "pct pct-edit cor-action-default-able04",
						tooltipText: this.traducao.getLabel("BTTN_EDIT"),
						actionFn: (row) => this.editarClassificacao(row),
					},
					{
						nome: this.traducao.getLabel("action-delete"),
						iconClass: "pct pct-trash-2 cor-action-default-risk04",
						tooltipText: this.traducao.getLabel("BTTN_DELETE"),
						actionFn: (row) => this.editarClassificacao(row),
					},
				],
			});
			this.cd.detectChanges();
		});
	}
}
