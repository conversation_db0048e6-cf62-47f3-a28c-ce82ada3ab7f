<adm-layout
	(goBack)="voltarListagem()"
	i18n-modulo="@@adm:moduleName"
	i18n-pageTitle="@@cad-aux:cidade:mainTitle"
	i18n-subtitle="@@cad-aux:subtitle"
	modulo="Administrativo"
	pageTitle="Cidade"
	subtitle="Informe os dados abaixo">
	<pacto-cat-card-plain>
		<div class="table-wrapper pacto-shadow">
			<div class="row">
				<!-- CÓDIGO -->
				<div class="col-12 col-sm-12 col-md-2 col-lg-2 col-xl-2">
					<pacto-cat-form-input-number
						[formControl]="codigoControl"
						i18n-label="@@cad-aux:cidade:codigoLabel"
						label="Código"
						placeholder="000"
						readonly="true"></pacto-cat-form-input-number>
				</div>

				<!-- NOME -->
				<div class="col-12 col-sm-12 col-md-6 col-lg-6 col-xl-6">
					<pacto-cat-form-input
						[control]="form.get('nome')"
						errorMsg="Insira um nome"
						i18n-errorMsg="@@cad-aux:cidade:inputErrorMsg"
						i18n-label="@@cad-aux:cidade:nomeFormLabel"
						i18n-placeholder="@@cad-aux:cidade:nomeFormPlaceholder"
						label="Nome"
						maxlength="50"
						placeholder="Nome"></pacto-cat-form-input>
				</div>

				<!-- PAÍS SELECT-->
				<div class="col-12 col-sm-12 col-md-3 col-lg-3 col-xl-3">
					<pacto-cat-form-select-filter
						[control]="form.get('pais')"
						[options]="paisArray"
						[paramBuilder]="paisSelectBuilder"
						errorMsg="Selecione um país"
						i18n-errorMsg="@@cad-aux:cidade:selectPaisError"
						i18n-label="@@cad-aux:cidade:selectPaisLabel"
						idKey="codigo"
						label="País"
						labelKey="nome"></pacto-cat-form-select-filter>
				</div>

				<!-- PAÍS BOTÕES-->
				<div
					class="col-9 col-sm-9 col-md-1 col-lg-1 col-xl-1 d-flex align-items-center">
					<div
						(click)="addPais()"
						class="add-pais"
						i18n="@@cad-aux:cidade:addPaisBttn">
						<i class="pct pct-plus-square"></i>
					</div>

					<div
						(click)="loadPais()"
						class="refresh-Pais"
						i18n="@@cad-aux:cidade:refreshPaisBttn">
						<i class="pct pct-refresh-cw"></i>
					</div>
				</div>
			</div>
		</div>

		<div class="table-wrapper pacto-shadow">
			<div class="row">
				<!-- ESTADO SELECT-->
				<div class="col-12 col-sm-12 col-md-3 col-lg-3 col-xl-3">
					<pacto-cat-form-select-filter
						[control]="form.get('estado')"
						[options]="estadoArray"
						[paramBuilder]="estadoSelectBuilder"
						errorMsg="Selecione um estado"
						i18n-errorMsg="@@cad-aux:cidade:selectEstadoErrorMsg"
						i18n-label="@@cad-aux:cidade:selectEstadolabel"
						idKey="codigo"
						label="Estado"
						labelKey="descricao"></pacto-cat-form-select-filter>
				</div>

				<!-- ESTADO BOTÃO-->
				<div class="d-flex align-items-center">
					<div
						(click)="
							loadEstados(
								form.get('pais').value
									? form.get('pais').value.codigo
									: undefined
							)
						"
						class="refresh-pais"
						i18n="@@cad-aux:cidade:refreshEstadosBttn">
						<i class="pct pct-refresh-cw"></i>
					</div>
				</div>

				<!-- IBGE -->
				<div class="col-12 col-sm-12 col-md-2 col-lg-2 col-xl-2">
					<pacto-cat-form-input-number
						[formControl]="form.get('codigoMunicipio')"
						i18n-label="@@cad-aux:cidade:codigoIBGE"
						label="Código Mun.IBGE"
						maxlength="20"
						placeholder="0000000"></pacto-cat-form-input-number>
				</div>

				<!-- HOMOLOGADA -->
				<div
					class="col-12 col-sm-12 col-md-2 col-lg-2 col-xl-2 d-flex align-items-center">
					<div class="homologada-checkbox">
						<pacto-cat-checkbox
							[control]="form.get('homologada')"
							i18n-label="@@cad-aux:cidade:checkboxHomologada"
							label="Homologada"></pacto-cat-checkbox>
					</div>
				</div>
			</div>

			<div class="row justify-content-end">
				<pacto-cat-button
					(click)="voltarListagem()"
					i18n-label="@@adm:cad-aux:cancelBttn"
					label="Cancelar"
					style="margin-right: 10px"
					type="OUTLINE_DARK"></pacto-cat-button>

				<pacto-cat-button
					(click)="salvarCidade()"
					i18n-label="@@adm:cad-aux:saveBttn"
					label="Salvar"
					style="margin-right: 10px"
					type="PRIMARY"></pacto-cat-button>
			</div>
		</div>
	</pacto-cat-card-plain>
</adm-layout>

<pacto-traducoes-xingling #traducoes>
	>
	<span i18n="@@cad-aux:cidadeForm:saveMsg" xingling="SAVE_MSG">
		Cidade cadastrada com sucesso!
	</span>
	<span i18n="@@cad-aux:cidadeForm:paisErrorMsg" xingling="PAIS_ERROR_MSG">
		Selecione um país!
	</span>
	<span i18n="@@cad-aux:addPais:novoPais" xingling="NOVO_PAIS">Novo País</span>
</pacto-traducoes-xingling>
