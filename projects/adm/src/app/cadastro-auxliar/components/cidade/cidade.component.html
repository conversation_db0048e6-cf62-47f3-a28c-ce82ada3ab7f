<adm-layout
	(goBack)="voltarHome()"
	i18n-modulo="@@adm:moduleName"
	i18n-pageTitle="@@cad-aux:cidade:mainTitle"
	modulo="Administrativo"
	pageTitle="Cidade">
	<div class="table-wrapper">
		<pacto-relatorio
			#tableData
			(btnAddClick)="novaCidade()"
			(iconClick)="iconClickFn($event)"
			(rowClick)="editarCidade($event)"
			[actionTitulo]="traducao.getLabel('table-column-acoes')"
			[enableDs3]="true"
			[showBtnAdd]="true"
			[showShare]="true"
			[table]="table"
			i18n-labelBtnAdd="@@label-add-btn"
			labelBtnAdd="Adicionar"
			telaId="cidade"></pacto-relatorio>
	</div>
</adm-layout>

<pacto-traducoes-xingling #traducao>
	<span i18n="@@cad-aux:cidade:edit" xingling="BTTN_EDIT">Editar cidade</span>
	<span i18n="@@cad-aux:cidade:delete" xingling="BTTN_DELETE">
		Excluir cidade
	</span>
	<span i18n="@@adm:action-editar" xingling="action-editar">Editar</span>
	<span i18n="@@adm:action-excluir" xingling="action-excluir">Excluir</span>
	<span i18n="@@cad-aux:cidade:permission" xingling="PERMISSION_MSG">
		Seu usuário não possui permissão, procure seu administrador.
	</span>
	<span i18n="@@cad-aux:cidade:deleteMsg" xingling="DELETE_MSG">
		Cidade excluída com sucesso.
	</span>
	<span
		i18n="@@cad-aux:cidade:table-column-acoes"
		xingling="table-column-acoes">
		Ações
	</span>
</pacto-traducoes-xingling>

<ng-template #columnCodigo>
	<span i18n="@@cad-aux:cidade:tableColumnCodigo">Código</span>
</ng-template>

<ng-template #columnNome>
	<span i18n="@@cad-aux:cidade:tableColumnNome">Nome</span>
</ng-template>

<ng-template #columnEstado>
	<span i18n="@@cad-aux:cidade:tableColumnEstado">Estado</span>
</ng-template>

<ng-template #columnPais>
	<span i18n="@@cad-aux:cidade:tableColumnPais">País</span>
</ng-template>
