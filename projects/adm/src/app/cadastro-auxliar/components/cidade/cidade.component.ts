import {
	ChangeDetectorRef,
	Component,
	OnInit,
	TemplateRef,
	ViewChild,
} from "@angular/core";
import { Router } from "@angular/router";
import { CadastroAuxApiCidadeService, Cidade } from "cadastro-aux-api";
import { SnotifyService } from "ng-snotify";
import { Api, PerfilAcessoRecurso, SessionService } from "sdk";
import {
	PactoDataGridConfig,
	RelatorioComponent,
	TraducoesXinglingComponent,
} from "ui-kit";
import { AdmRestService } from "../../../adm-rest.service";
import { PerfilAcessoRecursoNome } from "../../../perfil-acesso/perfil-acesso-recurso.model";

@Component({
	selector: "adm-cidade",
	templateUrl: "./cidade.component.html",
	styleUrls: ["./cidade.component.scss"],
})
export class CidadeComponent implements OnInit {
	@ViewChild("tableData", { static: false }) tableData: RelatorioComponent;
	@ViewChild("traducao", { static: true }) traducao: TraducoesXinglingComponent;
	@ViewChild("columnCodigo", { static: true }) columnCodigo: TemplateRef<any>;
	@ViewChild("columnNome", { static: true }) columnNome: TemplateRef<any>;
	@ViewChild("columnEstado", { static: true }) columnEstado: TemplateRef<any>;
	@ViewChild("columnPais", { static: true }) columnPais: TemplateRef<any>;
	table: PactoDataGridConfig;
	recurso: PerfilAcessoRecurso;

	constructor(
		private router: Router,
		private cd: ChangeDetectorRef,
		private session: SessionService,
		private admRest: AdmRestService,
		private notificationService: SnotifyService,
		private cidadeService: CadastroAuxApiCidadeService
	) {
		this.recurso = this.session.recursos.get(PerfilAcessoRecursoNome.CIDADE);
	}

	ngOnInit() {
		this.initTable();
	}

	editarCidade(cidade) {
		this.router.navigate(["adm", "cad-aux", "cidade", cidade.codigo]);
	}

	deleteCidade(cidade: Cidade) {
		if (this.recurso.excluir) {
			this.cidadeService.delete(cidade.codigo).subscribe(
				(response) => {
					this.notificationService.success(
						this.traducao.getLabel("DELETE_MSG")
					);
					this.tableData.reloadData();
				},
				(httpErrorResponse) => {
					const err = httpErrorResponse.error;
					if (err.meta && err.meta.messageValue) {
						this.notificationService.error(err.meta.messageValue);
					}
				}
			);
		} else {
			this.notificationService.warning(
				this.traducao.getLabel("PERMISSION_MSG")
			);
		}
	}

	novaCidade() {
		this.router.navigate(["adm", "cad-aux", "nova-cidade"]);
	}

	voltarHome() {
		this.router.navigate(["adm"]);
	}

	iconClickFn(event: { row: any; iconName: string }) {
		if (event.iconName === "action-edit (key)") {
			this.editarCidade(event.row);
		} else if (event.iconName === "action-delete (key)") {
			this.deleteCidade(event.row);
		} else {
			this.notificationService.warning(
				this.traducao.getLabel("PERMISSION_MSG")
			);
		}
	}

	private initTable() {
		setTimeout(() => {
			this.table = new PactoDataGridConfig({
				endpointUrl: this.admRest.buildFullUrl("/cidades", false, Api.MSCADAUX),
				quickSearch: true,
				ghostLoad: true,
				ghostAmount: 5,
				showFilters: true,
				columns: [
					{
						nome: "codigo",
						titulo: this.columnCodigo,
						visible: true,
						ordenavel: true,
					},
					{
						nome: "nome",
						titulo: this.columnNome,
						visible: true,
						ordenavel: true,
					},
					{
						nome: "estado",
						titulo: this.columnEstado,
						visible: true,
						ordenavel: false,
						valueTransform: (v) => v.descricao,
					},
					{
						nome: "pais",
						titulo: this.columnPais,
						visible: true,
						ordenavel: false,
						valueTransform: (v) => v.nome,
					},
				],
				actions: [
					{
						nome: this.traducao.getLabel("action-edit"),
						iconClass: "pct pct-edit cor-action-default-able04",
						tooltipText: this.traducao.getLabel("BTTN_EDIT"),
						actionFn: (row) => this.editarCidade(row),
					},
					{
						nome: this.traducao.getLabel("action-delete"),
						iconClass: "pct pct-trash-2 cor-action-default-risk04",
						tooltipText: this.traducao.getLabel("BTTN_DELETE"),
						actionFn: (row) => this.editarCidade(row),
					},
				],
			});
			this.cd.detectChanges();
		});
	}
}
