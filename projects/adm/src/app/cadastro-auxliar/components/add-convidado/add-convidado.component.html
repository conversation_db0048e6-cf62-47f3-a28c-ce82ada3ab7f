<adm-layout
	(goBack)="voltarAdm()"
	modulo="Administrativo/Cadastrar"
	pageTitle="Convidados">
	<pacto-cat-card-plain>
		<span class="title-head">
			Adicionando convidado de
			<span class="nome">{{ clienteNome }}</span>
		</span>
		<div class="row">
			<div class="col-4">
				<pacto-cat-form-select
					[control]="formGroup.get('consultor')"
					[items]="mockSelectObject"
					errorMsg="Selecione um tipo"
					label="Consultor"></pacto-cat-form-select>
			</div>
			<div class="col-4">
				<pacto-cat-form-input
					[control]="formGroup.get('cpf')"
					[textMask]="{ mask: cpfMask }"
					label="CPF"></pacto-cat-form-input>
			</div>
			<div class="col-4">
				<pacto-cat-form-input
					[control]="formGroup.get('nomeCompleto')"
					errorMsg="Informe o nome completo"
					label="Nome Completo"></pacto-cat-form-input>
			</div>
			<div class="col-4">
				<pacto-cat-form-input
					[control]="formGroup.get('nomeCompleto')"
					errorMsg="Informe o nome completo"
					label="Nome Completo"></pacto-cat-form-input>
			</div>

			<div class="col-4">
				<pacto-cat-form-input
					[control]="formGroup.get('nomeResponsavel')"
					errorMsg="Informe o nome responsavel"
					label="Nome responsavel"></pacto-cat-form-input>
			</div>
			<div class="col-4">
				<pacto-cat-form-datepicker
					[control]="formGroup.get('nascimento')"
					errorMsg="Forneça uma data válida."
					label="Nascimento"></pacto-cat-form-datepicker>
			</div>
			<div class="col-4">
				<pacto-cat-form-select
					[control]="formGroup.get('sexoBiologico')"
					[items]="mockSelectObject"
					errorMsg="Selecione um tipo"
					label="Sexo Biológico"></pacto-cat-form-select>
			</div>
			<div class="col-4">
				<pacto-cat-form-input
					[control]="formGroup.get('telefone')"
					[textMask]="{ mask: telMask }"
					label="Telefone"></pacto-cat-form-input>
			</div>
		</div>
		<div *ngIf="isEnderecoHidden">
			<span (click)="isEnderecoHidden = false" class="linkalike">
				Adicionar endereço
			</span>
		</div>
		<div *ngIf="!isEnderecoHidden">
			<hr />
			<div class="row d-flex align-items-center">
				<div class="col-3">
					<pacto-cat-form-input
						[control]="formGroup.get('cep')"
						[textMask]="{ mask: cepMask }"
						label="CEP"></pacto-cat-form-input>
				</div>
				<div class="col-4">
					<span (click)="consulteCep()" class="linkalike">Consulte o CEP</span>
				</div>
			</div>
			<div class="row">
				<div class="col-4">
					<pacto-cat-form-select
						[control]="formGroup.get('pais')"
						[items]="mockSelectObject"
						errorMsg="Selecione um tipo"
						label="País"></pacto-cat-form-select>
				</div>
				<div class="col-4">
					<pacto-cat-form-select
						[control]="formGroup.get('uf')"
						[items]="mockSelectObject"
						errorMsg="Selecione um tipo"
						label="UF"></pacto-cat-form-select>
				</div>
				<div class="col-4">
					<pacto-cat-form-select
						[control]="formGroup.get('cidade')"
						[items]="mockSelectObject"
						errorMsg="Selecione um tipo"
						label="Cidade"></pacto-cat-form-select>
				</div>
			</div>
			<div class="row">
				<div class="col-8">
					<pacto-cat-form-input
						[control]="formGroup.get('endereco')"
						label="Endereço"></pacto-cat-form-input>
				</div>
				<div class="col-2">
					<pacto-cat-form-input
						[control]="formGroup.get('complemento')"
						label="Complemento"></pacto-cat-form-input>
				</div>
				<div class="col-2">
					<pacto-cat-form-input-number
						[formControl]="formGroup.get('numero')"
						label="Número"></pacto-cat-form-input-number>
				</div>
			</div>
		</div>
		<div class="buttons d-flex justify-content-end">
			<pacto-cat-button
				(click)="lancarConvidado()"
				label="Lançar convidado"
				type="PRIMARY"></pacto-cat-button>
		</div>
	</pacto-cat-card-plain>
</adm-layout>
