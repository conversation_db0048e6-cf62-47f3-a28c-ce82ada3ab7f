import { Component, OnInit } from "@angular/core";
import { FormControl, FormGroup } from "@angular/forms";
import { SnotifyService } from "ng-snotify";

@Component({
	selector: "adm-add-convidado",
	templateUrl: "./add-convidado.component.html",
	styleUrls: ["./add-convidado.component.scss"],
})
export class AddConvidadoComponent implements OnInit {
	isEnderecoHidden: boolean = true;
	formGroup = new FormGroup({
		consultor: new FormControl(""),
		cpf: new FormControl(),
		nomeCompleto: new FormControl(""),
		nomeResponsavel: new FormControl(""),
		nascimento: new FormControl(""),
		sexoBiologico: new FormControl(""),
		telefone: new FormControl(""),
		cep: new FormControl(),
		pais: new FormControl(""),
		uf: new FormControl(""),
		cidade: new FormControl(""),
		endereco: new FormControl(""),
		complemento: new FormControl(""),
		numero: new FormControl(),
	});
	clienteNome;
	cpfMask = [
		/\d/,
		/\d/,
		/\d/,
		".",
		/\d/,
		/\d/,
		/\d/,
		".",
		/\d/,
		/\d/,
		/\d/,
		"-",
		/\d/,
		/\d/,
	];
	cepMask = [
		/[0-9]/,
		/[0-9]/,
		/[0-9]/,
		/[0-9]/,
		/[0-9]/,
		"-",
		/[0-9]/,
		/[0-9]/,
		/[0-9]/,
	];
	telMask = [
		"(",
		/[0-9]/,
		/[0-9]/,
		")",
		" ",
		/[0-]/,
		/[0-9]/,
		/[0-9]/,
		/[0-9]/,
		/[0-9]/,
		"-",
		/[0-]/,
		/[0-9]/,
		/[0-9]/,
		/[0-9]/,
	];
	mockSelectObject = [
		{ id: 0, label: "Mountain" },
		{ id: 1, label: "May" },
		{ id: 2, label: "Realize" },
		{ id: 3, label: "Firm" },
		{ id: 4, label: "Older" },
		{ id: 5, label: "Industrial" },
		{ id: 6, label: "Busy" },
		{ id: 7, label: "Nails" },
		{ id: 8, label: "Building" },
		{ id: 9, label: "Saw" },
	];

	constructor(private notificationService: SnotifyService) {}

	ngOnInit() {}

	consulteCep() {
		this.notificationService.success(
			`carregado com sucesso dados do cep ${this.formGroup.controls["cep"].value}`
		);
	}

	lancarConvidado() {
		this.notificationService.success("efetuado o lançar do convidado mock");
	}

	voltarAdm(): void {}
}
