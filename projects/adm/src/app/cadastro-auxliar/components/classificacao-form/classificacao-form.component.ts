import { AfterViewInit, Component, OnInit, ViewChild } from "@angular/core";
import { FormControl, FormGroup } from "@angular/forms";
import { ActivatedRoute, Router } from "@angular/router";
import {
	CadastroAuxApiClassificacaoService,
	Classificacao,
} from "cadastro-aux-api";
import { SnotifyService } from "ng-snotify";
import { TraducoesXinglingComponent } from "ui-kit";

@Component({
	selector: "adm-classificacao-form",
	templateUrl: "./classificacao-form.component.html",
	styleUrls: ["./classificacao-form.component.scss"],
})
export class ClassificacaoFormComponent implements OnInit, AfterViewInit {
	@ViewChild("traducoes", { static: true })
	traducao: TraducoesXinglingComponent;
	page = 0;
	form: FormGroup;
	codigoControl: FormControl = new FormControl();
	id;
	classificacao: Classificacao = new Classificacao();

	constructor(
		private classificacaoService: CadastroAuxApiClassificacaoService,
		private activatedRoute: ActivatedRoute,
		private router: Router,
		private notificationService: SnotifyService
	) {}

	ngOnInit() {
		this.createForm();
		this.id = this.activatedRoute.snapshot.paramMap.get("id");
		this.codigoControl.disable();
	}

	ngAfterViewInit() {
		if (this.id) {
			this.classificacaoService.find(this.id).subscribe((response) => {
				this.classificacao = response.content;
				this.codigoControl.setValue(this.classificacao.codigo);
				this.form.patchValue({
					nome: this.classificacao.nome,
					enviarsmsautomatico: this.classificacao.enviarsmsautomatico,
				});
			});
		}
	}

	createForm() {
		this.form = new FormGroup({
			nome: new FormControl(),
			enviarsmsautomatico: new FormControl(),
		});
	}

	voltarListagem() {
		this.router.navigate(["adm", "cad-aux", "classificacao"]);
	}

	salvarClassificacao() {
		Object.keys(this.form.getRawValue()).forEach((key) => {
			this.classificacao[key] = this.form.getRawValue()[key];
		});
		this.classificacaoService.save(this.classificacao).subscribe(
			(response) => {
				const mensagem = this.id
					? this.traducao.getLabel("EDIT_MSG")
					: this.traducao.getLabel("SAVE_MSG");

				this.notificationService.success(mensagem);
				this.voltarListagem();
			},
			(httpErrorResponse) => {
				const err = httpErrorResponse.error;
				if (err.meta && err.meta.messageValue) {
					this.notificationService.error(err.meta.messageValue);
				}
			}
		);
	}
}
