import {
	AfterViewInit,
	ChangeDetectorRef,
	Component,
	OnInit,
	ViewChild,
} from "@angular/core";
import { FormControl, FormGroup } from "@angular/forms";
import { ActivatedRoute, Router } from "@angular/router";
import { AdmMsApiConfiguracaoService } from "adm-ms-api";
import { CadastroAuxApiCategoriaService, Categoria } from "cadastro-aux-api";
import { SnotifyService } from "ng-snotify";
import { PermissaoService } from "pacto-layout";
import { TraducoesXinglingComponent } from "ui-kit";
import { getTipoCategoriaText } from "../../enums/TipoCategoria";

@Component({
	selector: "adm-categoria-form",
	templateUrl: "./categoria-form.component.html",
	styleUrls: ["./categoria-form.component.scss"],
})
export class CategoriaFormComponent implements OnInit, AfterViewInit {
	@ViewChild("traducao", { static: true }) traducao: TraducoesXinglingComponent;
	codigoControl: FormControl = new FormControl();
	categoria: Categoria = new Categoria();
	getTipoCategoriaText: Array<{ id: string; label: string }> = new Array<{
		id: string;
		label: string;
	}>();

	form: FormGroup;
	id;
	utilizarservicosesisc: boolean = false;

	constructor(
		private router: Router,
		private categoriaService: CadastroAuxApiCategoriaService,
		private activatedRoute: ActivatedRoute,
		private notificationService: SnotifyService,
		private cd: ChangeDetectorRef,
		private admConfigApiService: AdmMsApiConfiguracaoService,
		private permissaoService: PermissaoService
	) {}

	ngOnInit() {
		this.createForm();
		this.codigoControl.disable();
		this.id = this.activatedRoute.snapshot.paramMap.get("id");

		if (this.id) {
			this.categoriaService.find(this.id).subscribe((response) => {
				this.categoria = response.content;
				this.codigoControl.setValue(this.categoria.codigo);
				this.form.patchValue({
					nome: this.categoria.nome,
					tipoCategoria: this.categoria.tipoCategoria,
					nomeExterno: this.categoria.nomeExterno,
					validarSituacaoEmpresaSesi: this.categoria.validarSituacaoEmpresaSesi,
					obrigatorioCnpjClienteSesi: this.categoria.obrigatorioCnpjClienteSesi,
					codigoClientela: this.categoria.codigoClientela,
				});
			});
		}
	}

	ngAfterViewInit() {
		this.initTipoCategoriaArray();
		this.admConfigApiService.consultar().subscribe((ret) => {
			this.utilizarservicosesisc = ret.utilizarservicosesisc;
			this.cd.detectChanges();
		});
	}

	createForm() {
		this.form = new FormGroup({
			nome: new FormControl(),
			tipoCategoria: new FormControl(),
			nomeExterno: new FormControl(),
			obrigatorioCnpjClienteSesi: new FormControl(),
			validarSituacaoEmpresaSesi: new FormControl(),
			codigoClientela: new FormControl(),
		});
	}

	utilizaServicoSesiSc() {
		return this.utilizarservicosesisc;
	}

	salvarCategoria() {
		Object.keys(this.form.getRawValue()).forEach((key) => {
			this.categoria[key] = this.form.getRawValue()[key];
		});
		this.categoriaService.save(this.categoria).subscribe(
			(response) => {
				this.notificationService.success(this.traducao.getLabel("SAVE_MSG"));
				this.voltarListagem();
			},
			(httpErrorResponse) => {
				const err = httpErrorResponse.error;
				if (err.meta && err.meta.messageValue) {
					this.notificationService.error(err.meta.messageValue);
				}
			}
		);
	}

	habilitadoCadastroEmpresaSesi() {
		return this.permissaoService.temConfiguracaoEmpresaAdm(
			"habilitarCadastroEmpresaSesi"
		);
	}

	voltarListagem() {
		this.router.navigate(["adm", "cad-aux", "categoria"]);
	}

	private initTipoCategoriaArray() {
		this.getTipoCategoriaText = getTipoCategoriaText(this.traducao);
		this.cd.detectChanges();
	}
}
