import {
	AfterViewInit,
	Component,
	OnInit,
	TemplateRef,
	ViewChild,
} from "@angular/core";

import { FormControl, FormGroup } from "@angular/forms";
import { ActivatedRoute, Router } from "@angular/router";
import { NgbActiveModal } from "@ng-bootstrap/ng-bootstrap";
import {
	CadastroAuxApiEstadoService,
	CadastroAuxApiPaisService,
	Estado,
	Pais,
} from "cadastro-aux-api";
import { SnotifyService } from "ng-snotify";
import {
	PactoDataGridConfig,
	RelatorioComponent,
	TraducoesXinglingComponent,
} from "ui-kit";

@Component({
	selector: "adm-add-pais",
	templateUrl: "./add-pais.component.html",
	styleUrls: ["./add-pais.component.scss"],
})
export class AddPaisComponent implements OnInit, AfterViewInit {
	@ViewChild("tableEstadosComponent", { static: true })
	tableEstadosComponent: RelatorioComponent;
	@ViewChild("traducao", { static: true }) traducao: TraducoesXinglingComponent;
	@ViewChild("columnSigla", { static: true }) columnSigla: TemplateRef<any>;
	@ViewChild("columnDescricao", { static: true })
	columnDescricao: TemplateRef<any>;
	page = 0;
	pais: Pais = new Pais();
	form: FormGroup;
	formEstado: FormGroup;
	codigoControl: FormControl = new FormControl();
	tableEstados: PactoDataGridConfig;
	estados: {
		content: Array<Estado>;
		first: boolean;
		last: boolean;
		number: number;
		size: number;
		totalElements: number;
		totalPages: number;
	} = {
		content: new Array<Estado>(),
		first: true,
		last: false,
		number: 0,
		size: 0,
		totalElements: 0,
		totalPages: 0,
	};
	id;

	constructor(
		private paisService: CadastroAuxApiPaisService,
		private activatedRoute: ActivatedRoute,
		private router: Router,
		private notificationService: SnotifyService,
		private estadoService: CadastroAuxApiEstadoService,
		public dialog: NgbActiveModal
	) {}

	ngOnInit() {
		this.createForm();
		this.createFormEstado();
		this.initTableEstado();
		this.id = this.activatedRoute.snapshot.paramMap.get("id");
		this.codigoControl.disable();
	}

	ngAfterViewInit() {
		console.log(this.tableEstadosComponent);
		if (this.id) {
			this.paisService.find(this.id).subscribe((response) => {
				this.pais = response.content;
				this.codigoControl.setValue(this.pais.codigo);
				this.form.patchValue({
					nome: this.pais.nome,
					nacionalidade: this.pais.nacionalidade,
				});
				this.sortEstados();
				this.createEstadoPageObject();
			});
		}
	}

	createEstadoPageObject(page = 1, size = 10) {
		this.estados.totalElements = this.pais.estados.length;
		this.estados.size = size;
		this.estados.totalPages = +(this.estados.totalElements / this.estados.size);
		this.estados.first = page === 0 || page === 1;
		this.estados.last = page === this.estados.totalPages;
		this.estados.content = this.pais.estados.slice(
			size * page - size,
			size * page
		);
		this.tableEstadosComponent.reloadData();
	}

	initTableEstado() {
		this.tableEstados = new PactoDataGridConfig({
			dataAdapterFn: (serverData) => {
				return this.estados;
			},
			columns: [
				{
					nome: "sigla",
					titulo: this.columnSigla,
					visible: true,
					ordenavel: false,
				},
				{
					nome: "descricao",
					titulo: this.columnDescricao,
					visible: true,
					ordenavel: false,
				},
			],
			actions: [
				{
					nome: this.traducao.getLabel("ACTION_DELETE"),
					iconClass: "pct pct-trash-2 cor-hellboy-pri",
					tooltipText: "",
				},
			],
		});
	}

	createForm() {
		this.form = new FormGroup({
			nome: new FormControl(),
			nacionalidade: new FormControl(),
		});
	}

	createFormEstado() {
		this.formEstado = new FormGroup({
			sigla: new FormControl(),
			descricao: new FormControl(),
		});
	}

	cancel() {
		this.dialog.dismiss();
	}

	addEstado() {
		this.pais.estados.push(this.formEstado.getRawValue());
		this.formEstado.setValue(new Estado());
		this.sortEstados();
		this.createEstadoPageObject();
	}

	deleteEstado(rowInfo) {
		const estado = rowInfo.row;
		if (estado.codigo) {
			this.estadoService.verificaVinculoCidade(estado.codigo).subscribe(
				(response) => {
					let index = null;
					this.pais.estados.find((estado2, ind) => {
						if (estado2.codigo === estado.codigo) {
							index = ind;
							return estado2;
						}
					});
					this.pais.estados.splice(index, 1);
					this.sortEstados();
					this.createEstadoPageObject();
				},
				(httpErrorResponse) => {
					const err = httpErrorResponse.error;
					if (err.meta && err.meta.messageValue) {
						this.notificationService.error(err.meta.messageValue);
					}
				}
			);
		} else {
			this.pais.estados.splice(rowInfo.rowIndex, 1);
			this.sortEstados();
			this.createEstadoPageObject();
		}
	}

	salvarPais() {
		Object.keys(this.form.getRawValue()).forEach((key) => {
			this.pais[key] = this.form.getRawValue()[key];
		});
		this.paisService.save(this.pais).subscribe(
			(response) => {
				this.notificationService.success(
					this.traducao.getLabel("PAIS_SUCCESS")
				);
				this.dialog.close(response.content);
			},
			(httpErrorResponse) => {
				const err = httpErrorResponse.error;
				if (err.meta && err.meta.messageValue) {
					this.notificationService.error(err.meta.messageValue);
				}
			}
		);
	}

	changePageEstados(page) {
		this.page = page;
		this.createEstadoPageObject(page);
	}

	changePageSizeEstados(size) {
		this.createEstadoPageObject(this.page, size);
	}

	sortEstados() {
		this.pais.estados = this.pais.estados.sort((a, b) => {
			if (a.descricao > b.descricao) {
				return 1;
			} else if (a.descricao < b.descricao) {
				return -1;
			} else {
				return 0;
			}
		});
	}
}
