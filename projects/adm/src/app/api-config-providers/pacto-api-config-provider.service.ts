import { Injectable } from "@angular/core";

import { PactoApiConfig, PactoApiConfigProviderBase } from "pacto-api";
import { Observable, of } from "rxjs";
import { ClientDiscoveryService } from "sdk";

@Injectable()
export class PactoApiConfigProvider extends PactoApiConfigProviderBase {
	constructor(private discoveryService: ClientDiscoveryService) {
		super();
	}

	getApiConfig(): Observable<PactoApiConfig> {
		const baseUrl = `${this.discoveryService.getUrlMap().apiZwUrl}`;
		const authToken = localStorage.getItem("apiToken");

		return of({
			authToken,
			baseUrl,
		});
	}
}
