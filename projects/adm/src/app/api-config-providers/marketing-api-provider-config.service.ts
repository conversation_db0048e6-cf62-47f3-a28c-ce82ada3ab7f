import { Inject, Injectable, LOCALE_ID } from "@angular/core";
import {
	MarketingApiConfig,
	MarketingApiConfigProviderBase,
} from "marketing-api";
import { Observable, of } from "rxjs";
import { ClientDiscoveryService } from "sdk";

@Injectable()
export class MarketingApiProviderConfigService extends MarketingApiConfigProviderBase {
	constructor(
		@Inject(LOCALE_ID) private readonly locale,
		private readonly discoveryService: ClientDiscoveryService
	) {
		super();
	}

	getApiConfig(): Observable<MarketingApiConfig> {
		const baseUrl = this.discoveryService.getUrlMap().urlMarketingMs;
		const authToken = localStorage.getItem("apiToken");

		return of({
			authToken,
			baseUrl,
			acceptLanguage: this.locale,
		});
	}
}
