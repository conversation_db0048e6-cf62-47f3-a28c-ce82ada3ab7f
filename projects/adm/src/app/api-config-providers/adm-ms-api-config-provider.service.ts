import { Inject, Injectable, LOCALE_ID } from "@angular/core";
import { AdmCoreApiConfig } from "adm-core-api";
import { Observable, of } from "rxjs";
import { ClientDiscoveryService, SessionService } from "sdk";
import { AdmMsApiConfigProviderBase } from "../../../../adm-ms-api/src/lib/adm-ms-api-config-provider-base.service";

@Injectable({
	providedIn: "root",
})
export class AdmMsApiConfigProviderService extends AdmMsApiConfigProviderBase {
	constructor(
		private sessionService: SessionService,
		private discoveryService: ClientDiscoveryService,
		@Inject(LOCALE_ID) private locale
	) {
		super();
	}

	getApiConfig(): Observable<AdmCoreApiConfig> {
		const empresaId = this.sessionService.empresaId;

		return of({
			baseUrl: this.discoveryService.getUrlMap().admMsUrl,
			authToken: localStorage.getItem("apiToken"),
			empresaId: empresaId ? `${empresaId}` : null,
			acceptLanguage: this.locale,
			zwJSId: this.sessionService.zwJSId,
		});
	}
}
