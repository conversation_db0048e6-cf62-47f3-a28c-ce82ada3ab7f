import { Inject, Injectable, LOCALE_ID } from "@angular/core";

import {
	CadastroAuxApiConfig,
	CadastroAuxApiConfigProviderBase,
} from "cadastro-aux-api";
import { Observable, of } from "rxjs";
import { ClientDiscoveryService, SessionService } from "sdk";

@Injectable()
export class CadastroAuxApiConfigProviderService extends CadastroAuxApiConfigProviderBase {
	constructor(
		private discoveryService: ClientDiscoveryService,
		private sessionService: SessionService,
		@Inject(LOCALE_ID) private locale
	) {
		super();
	}

	getApiConfig(): Observable<CadastroAuxApiConfig> {
		const urlMap = this.discoveryService.getUrlMap();
		return of({
			baseUrl: urlMap.cadastroAuxiliarUrl,
			authToken: this.sessionService.token,
			empresaId: this.sessionService.empresaId,
			acceptLanguage: this.locale,
		});
	}
}
