import { Injectable } from "@angular/core";
import {
	AcessoSistemaApiConfig,
	AcessoSistemaApiConfigProviderBase,
} from "acesso-sistema-api";
import { Observable, of } from "rxjs";
import { ClientDiscoveryService, SessionService } from "sdk";

@Injectable()
export class AcessoSistemaApiProviderConfigService extends AcessoSistemaApiConfigProviderBase {
	constructor(
		private sessionService: SessionService,
		private discoveryService: ClientDiscoveryService
	) {
		super();
	}

	getApiConfig(): Observable<AcessoSistemaApiConfig> {
		const empresaId = this.sessionService.empresaId;
		const baseUrl = this.discoveryService.getUrlMap().acessoSistemaMsUrl;
		const authToken = localStorage.getItem("apiToken");

		return of({
			authToken,
			baseUrl,
			empresaId: empresaId ? `${empresaId}` : null,
		});
	}
}
