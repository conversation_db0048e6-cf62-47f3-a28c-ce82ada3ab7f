import { Injectable } from "@angular/core";
import {
	MsPactopayApiConfig,
	MsPactopayApiConfigProviderBase,
} from "ms-pactopay-api";
import { Observable, of } from "rxjs";
import { SessionService } from "sdk";
import { ClientDiscoveryService } from "src/app/microservices/client-discovery/client-discovery.service";

@Injectable()
export class MsPactopayApiConfigProvider extends MsPactopayApiConfigProviderBase {
	constructor(
		private sessionService: SessionService,
		private discoveryService: ClientDiscoveryService
	) {
		super();
	}

	getApiConfig(): Observable<MsPactopayApiConfig> {
		const empresaId = this.sessionService.empresaId;
		const baseUrl = this.discoveryService.getUrlMap().pactoPayMsUrl;
		const authToken = localStorage.getItem("apiToken");

		return of({
			authToken,
			baseUrl,
			empresaId: empresaId ? `${empresaId}` : null,
		});
	}
}
