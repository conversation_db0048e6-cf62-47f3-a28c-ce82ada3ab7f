import { Inject, Injectable, LOCALE_ID } from "@angular/core";
import {
	LoginAppApiConfig,
	LoginAppApiConfigProviderBase,
} from "login-app-api";
import { Observable, of } from "rxjs";
import { ClientDiscoveryService } from "sdk";

@Injectable()
export class LoginAppApiConfigProviderService extends LoginAppApiConfigProviderBase {
	constructor(
		@Inject(LOCALE_ID) private readonly locale,
		private readonly discoveryService: ClientDiscoveryService
	) {
		super();
	}

	getApiConfig(): Observable<LoginAppApiConfig> {
		const baseUrl = this.discoveryService.getUrlMap().loginAppUrl;
		const authToken = localStorage.getItem("apiToken");

		return of({
			authToken,
			baseUrl,
			acceptLanguage: this.locale,
		});
	}
}
