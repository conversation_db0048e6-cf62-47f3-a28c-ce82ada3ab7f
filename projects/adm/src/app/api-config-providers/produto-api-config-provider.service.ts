import { Inject, Injectable, LOCALE_ID } from "@angular/core";
import { ProdutoApiConfig, ProdutoApiConfigProviderBase } from "produto-api";
import { Observable, of } from "rxjs";

import { ClientDiscoveryService, SessionService } from "sdk";

@Injectable({
	providedIn: "root",
})
export class ProdutoApiConfigProviderService extends ProdutoApiConfigProviderBase {
	constructor(
		private discoveryService: ClientDiscoveryService,
		private sessionService: SessionService,
		@Inject(LOCALE_ID) private locale
	) {
		super();
	}

	getApiConfig(): Observable<ProdutoApiConfig> {
		const urlMap = this.discoveryService.getUrlMap();
		return of({
			baseUrl: urlMap.produtoMsUrl,
			authToken: this.sessionService.token,
			empresaId: this.sessionService.empresaId,
			acceptLanguage: this.locale,
		});
	}
}
