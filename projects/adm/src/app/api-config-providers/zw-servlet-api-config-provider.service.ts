import { Injectable } from "@angular/core";
import { Observable, of } from "rxjs";
import { ClientDiscoveryService, SessionService } from "sdk";

import {
	ZwServletApiConfig,
	ZwServletApiConfigProviderBase,
} from "zw-servlet-api";

@Injectable({
	providedIn: "root",
})
export class ZwServletApiConfigProvider extends ZwServletApiConfigProviderBase {
	constructor(
		private sessionService: SessionService,
		private discoveryService: ClientDiscoveryService
	) {
		super();
	}

	getApiConfig(): Observable<ZwServletApiConfig> {
		const baseUrl = this.discoveryService.getUrlMap().zwUrl;
		const key = this.sessionService.chave;
		const origin = window.location.host;
		const empresa = this.sessionService.empresaId;
		const username = this.sessionService.loggedUser.username;

		return of({
			baseUrl,
			key,
			origin,
			empresa,
			username,
		});
	}
}
