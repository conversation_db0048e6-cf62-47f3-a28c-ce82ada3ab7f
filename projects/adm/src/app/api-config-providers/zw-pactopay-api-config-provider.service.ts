import { Injectable } from "@angular/core";
import { Observable, of } from "rxjs";
import { ClientDiscoveryService, SessionService } from "sdk";

import {
	ZwPactopayApiConfig,
	ZwPactopayApiConfigProviderBase,
} from "zw-pactopay-api";

@Injectable()
export class ZwPactopayApiConfigProvider extends ZwPactopayApiConfigProviderBase {
	constructor(
		private sessionService: SessionService,
		private discoveryService: ClientDiscoveryService
	) {
		super();
	}

	getApiConfig(): Observable<ZwPactopayApiConfig> {
		const baseUrl = `${this.discoveryService.getUrlMap().zwUrl}/prest/pactopay`;
		const key = this.sessionService.chave;
		const origin = window.location.host;
		const empresa = this.sessionService.empresaId;
		const username = this.sessionService.loggedUser.username;

		return of({
			baseUrl,
			key,
			origin,
			empresa,
			username,
		});
	}
}
