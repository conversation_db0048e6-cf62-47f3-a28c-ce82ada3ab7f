import { Inject, Injectable, LOCALE_ID } from "@angular/core";
import { PlanoApiConfig, PlanoApiConfigProviderBase } from "plano-api";
import { Observable, of } from "rxjs";

import { ClientDiscoveryService, SessionService } from "sdk";

@Injectable({
	providedIn: "root",
})
export class PlanoApiConfigProviderService extends PlanoApiConfigProviderBase {
	constructor(
		private discoveryService: ClientDiscoveryService,
		private sessionService: SessionService,
		@Inject(LOCALE_ID) private locale
	) {
		super();
	}

	getApiConfig(): Observable<PlanoApiConfig> {
		const urlMap = this.discoveryService.getUrlMap();
		return of({
			baseUrl: urlMap.planoMsUrl,
			authToken: this.sessionService.token,
			empresaId: this.sessionService.empresaId,
			locale: this.locale,
		});
	}
}
