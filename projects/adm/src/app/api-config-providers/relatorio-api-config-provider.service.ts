import { Inject, Injectable, LOCALE_ID } from "@angular/core";

import {
	RelatorioApiConfig,
	RelatorioApiConfigProviderBase,
} from "relatorio-api";
import { Observable, of } from "rxjs";
import { ClientDiscoveryService, SessionService } from "sdk";

@Injectable()
export class RelatorioApiConfigProviderService extends RelatorioApiConfigProviderBase {
	constructor(
		private discoveryService: ClientDiscoveryService,
		private sessionService: SessionService,
		@Inject(LOCALE_ID) private locale
	) {
		super();
	}

	getApiConfig(): Observable<RelatorioApiConfig> {
		const urlMap = this.discoveryService.getUrlMap();
		return of({
			baseUrl: urlMap.relatorioMsUrl,
			authToken: this.sessionService.token,
			empresaId: this.sessionService.empresaId,
			acceptLanguage: this.locale,
		});
	}
}
