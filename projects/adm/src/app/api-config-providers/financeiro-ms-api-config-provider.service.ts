import { Inject, Injectable, LOCALE_ID } from "@angular/core";
import { AdmCoreApiConfig } from "adm-core-api";
import { FinanceiroMsApiConfigProviderBase } from "financeiro-ms-api";
import { Observable, of } from "rxjs";
import { ClientDiscoveryService, SessionService } from "sdk";

@Injectable({
	providedIn: "root",
})
export class FinanceiroMsApiConfigProviderService extends FinanceiroMsApiConfigProviderBase {
	constructor(
		private sessionService: SessionService,
		private discoveryService: ClientDiscoveryService,
		@Inject(LOCALE_ID) private locale
	) {
		super();
	}

	getApiConfig(): Observable<AdmCoreApiConfig> {
		const empresaId = this.sessionService.empresaId;

		return of({
			baseUrl: this.discoveryService.getUrlMap().financeiroMsUrl,
			authToken: localStorage.getItem("apiToken"),
			empresaId: empresaId ? `${empresaId}` : null,
			acceptLanguage: this.locale,
		});
	}
}
