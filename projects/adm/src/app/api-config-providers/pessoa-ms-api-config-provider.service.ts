import { Inject, Injectable, LOCALE_ID } from "@angular/core";
import {
	PessoaMsApiConfig,
	PessoaMsApiConfigProviderBase,
} from "pessoa-ms-api";
import { Observable, of } from "rxjs";
import { ClientDiscoveryService, SessionService } from "sdk";

@Injectable()
export class PessoaMsApiConfigProviderService extends PessoaMsApiConfigProviderBase {
	constructor(
		private sessionService: SessionService,
		private discoveryService: ClientDiscoveryService,
		@Inject(LOCALE_ID) private locale
	) {
		super();
	}

	getApiConfig(): Observable<PessoaMsApiConfig> {
		const empresaId = this.sessionService.empresaId;
		return of({
			baseUrl: this.discoveryService.getUrlMap().pessoaMsUrl,
			authToken: localStorage.getItem("apiToken"),
			empresaId: empresaId ? `${empresaId}` : null,
			acceptLanguage: this.locale,
		});
	}
}
