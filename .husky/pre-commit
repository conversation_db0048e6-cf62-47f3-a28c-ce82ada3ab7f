#!/bin/sh

# Ignora o hook se for um merge
if git rev-parse -q --verify MERGE_HEAD >/dev/null; then
  echo "Merge em andamento. Ignorando pre-commit..."
  exit 0
fi

# Check if "$(dirname "$0")/_/husky.sh" exists
if [ ! -f "$(dirname "$0")/_/husky.sh" ]; then
  echo "husky.sh not found, running husky install..."
  npm run husky:install
else
  echo "husky.sh found, skipping husky install."
  . "$(dirname "$0")/_/husky.sh"
fi

npm run lint-staged
