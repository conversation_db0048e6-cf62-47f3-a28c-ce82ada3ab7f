import { by, element } from "protractor";
import { LoginInternoPage } from "./login-interno.po";

describe("Login Interno", () => {
	let page: LoginInternoPage;

	beforeEach(() => {
		page = new LoginInternoPage();
		page.navigateTo();
		page.preencherCredenciais();
	});

	it("Cadastro do aluno pelo atalho", () => {
		element(by.id("adicionar-aluno-atalho")).click();
		element(by.id("nome-aluno-input")).sendKeys(
			"PRIMEIRO ALUNO DO TESTE AUTOMATIZADO"
		);

		element(by.id("pacto-select-professor")).click();
		element.all(by.css(".pacto-select-professor-item")).get(0).click();

		element(by.id("salvar-cadastro-aluno")).click();
	});
});
